/**
 * React Environment Tests
 * Tests storage functionality in React components and React-specific patterns
 */

/// <reference path="./jest-dom.d.ts" />

import React, { useState, useEffect } from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { initializeStorage, getRepositoryStorage } from '../../index';
import { IStorageAdapter } from '../../types';
import { mockBrowserEnvironment } from '../utils/test-helpers';

// Mock localforage for React tests
jest.mock('localforage', () => {
  const mockStore = new Map();
  
  return {
    createInstance: jest.fn(() => ({
      ready: jest.fn().mockResolvedValue(undefined),
      getItem: jest.fn((key) => Promise.resolve(mockStore.get(key) || null)),
      setItem: jest.fn((key, value) => {
        mockStore.set(key, value);
        return Promise.resolve(value);
      }),
      removeItem: jest.fn((key) => {
        mockStore.delete(key);
        return Promise.resolve();
      }),
      clear: jest.fn(() => {
        mockStore.clear();
        return Promise.resolve();
      }),
      keys: jest.fn(() => Promise.resolve(Array.from(mockStore.keys()))),
      length: jest.fn(() => Promise.resolve(mockStore.size)),
      driver: jest.fn(() => 'asyncStorage')
    })),
    INDEXEDDB: 'asyncStorage',
    LOCALSTORAGE: 'localStorageWrapper',
    WEBSQL: 'webSQLStorage'
  };
});

// Test component that uses storage
const StorageTestComponent: React.FC<{
  repositoryName: string;
  initialValue?: string;
  onStorageReady?: (storage: IStorageAdapter) => void;
}> = ({ repositoryName, initialValue, onStorageReady }) => {
  const [storage, setStorage] = useState<IStorageAdapter | null>(null);
  const [value, setValue] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initStorage = async () => {
      try {
        await initializeStorage();
        const storageInstance = await getRepositoryStorage(repositoryName);
        setStorage(storageInstance);
        onStorageReady?.(storageInstance);

        if (initialValue) {
          await storageInstance.setItem('test_value', initialValue);
        }

        const storedValue = await storageInstance.getItem('test_value');
        setValue((storedValue as string) || '');
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    initStorage();
  }, [repositoryName, initialValue, onStorageReady]);

  const updateValue = async (newValue: string) => {
    if (storage) {
      try {
        await storage.setItem('test_value', newValue);
        setValue(newValue);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Update error');
      }
    }
  };

  if (loading) return <div data-testid="loading">Loading...</div>;
  if (error) return <div data-testid="error">{error}</div>;

  return (
    <div>
      <div data-testid="value">{value}</div>
      <button 
        data-testid="update-button" 
        onClick={() => updateValue('updated_value')}
      >
        Update Value
      </button>
      <div data-testid="storage-platform">
        {storage?.getCapabilities().platform}
      </div>
    </div>
  );
};

// Custom hook for storage
const useStorage = (repositoryName: string) => {
  const [storage, setStorage] = useState<IStorageAdapter | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initStorage = async () => {
      try {
        await initializeStorage();
        const storageInstance = await getRepositoryStorage(repositoryName);
        setStorage(storageInstance);
        setIsReady(true);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Storage initialization failed');
      }
    };

    initStorage();
  }, [repositoryName]);

  const setItem = async (key: string, value: any) => {
    if (!storage) throw new Error('Storage not ready');
    return storage.setItem(key, value);
  };

  const getItem = async (key: string) => {
    if (!storage) throw new Error('Storage not ready');
    return storage.getItem(key);
  };

  const removeItem = async (key: string) => {
    if (!storage) throw new Error('Storage not ready');
    return storage.removeItem(key);
  };

  return {
    storage,
    isReady,
    error,
    setItem,
    getItem,
    removeItem
  };
};

// Test component using custom hook
const HookTestComponent: React.FC<{ repositoryName: string }> = ({ repositoryName }) => {
  const { storage, isReady, error, setItem, getItem } = useStorage(repositoryName);
  const [value, setValue] = useState<string>('');

  const handleLoad = async () => {
    try {
      const storedValue = await getItem('hook_test');
      setValue((storedValue as string) || 'no_value');
    } catch (err) {
      setValue('error');
    }
  };

  const handleSave = async () => {
    try {
      await setItem('hook_test', 'hook_value');
      setValue('hook_value');
    } catch (err) {
      setValue('save_error');
    }
  };

  if (!isReady) return <div data-testid="hook-loading">Loading...</div>;
  if (error) return <div data-testid="hook-error">{error}</div>;

  return (
    <div>
      <div data-testid="hook-value">{value}</div>
      <button data-testid="load-button" onClick={handleLoad}>Load</button>
      <button data-testid="save-button" onClick={handleSave}>Save</button>
      <div data-testid="hook-platform">{storage?.getCapabilities().platform}</div>
    </div>
  );
};

describe('React Environment Tests', () => {
  let cleanupBrowserMock: () => void;

  beforeEach(() => {
    cleanupBrowserMock = mockBrowserEnvironment();
  });

  afterEach(() => {
    cleanupBrowserMock();
    jest.clearAllMocks();
  });

  describe('React Component Integration', () => {
    it('should initialize storage in React component', async () => {
      render(<StorageTestComponent repositoryName="react_test" />);

      // Should show loading initially
      expect(screen.getByTestId('loading')).toBeInTheDocument();

      // Wait for storage to initialize
      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
      });

      // Should show storage platform
      expect(screen.getByTestId('storage-platform')).toHaveTextContent('browser');
    });

    it('should handle storage operations in React component', async () => {
      render(<StorageTestComponent repositoryName="react_ops" initialValue="initial" />);

      await waitFor(() => {
        expect(screen.getByTestId('value')).toHaveTextContent('initial');
      });

      // Update value
      act(() => {
        screen.getByTestId('update-button').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('value')).toHaveTextContent('updated_value');
      });
    });

    it('should handle storage errors in React component', async () => {
      // Mock storage to throw error
      const mockError = new Error('Storage error');
      jest.spyOn(console, 'error').mockImplementation(() => {});
      
      const originalGetRepositoryStorage = require('../../index').getRepositoryStorage;
      jest.spyOn(require('../../index'), 'getRepositoryStorage').mockRejectedValue(mockError);

      render(<StorageTestComponent repositoryName="error_test" />);

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Storage error');
      });

      // Restore
      require('../../index').getRepositoryStorage = originalGetRepositoryStorage;
      jest.restoreAllMocks();
    });

    it('should provide storage instance to callback', async () => {
      let capturedStorage: IStorageAdapter | null = null;
      
      const handleStorageReady = (storage: IStorageAdapter) => {
        capturedStorage = storage;
      };

      render(
        <StorageTestComponent 
          repositoryName="callback_test" 
          onStorageReady={handleStorageReady}
        />
      );

      await waitFor(() => {
        expect(capturedStorage).not.toBeNull();
      });

      expect(capturedStorage!.getCapabilities().platform).toBe('browser');
    });
  });

  describe('Custom React Hook Integration', () => {
    it('should provide storage through custom hook', async () => {
      render(<HookTestComponent repositoryName="hook_test" />);

      // Should show loading initially
      expect(screen.getByTestId('hook-loading')).toBeInTheDocument();

      // Wait for hook to initialize
      await waitFor(() => {
        expect(screen.queryByTestId('hook-loading')).not.toBeInTheDocument();
      });

      // Should show platform
      expect(screen.getByTestId('hook-platform')).toHaveTextContent('browser');
    });

    it('should handle storage operations through custom hook', async () => {
      render(<HookTestComponent repositoryName="hook_ops" />);

      await waitFor(() => {
        expect(screen.queryByTestId('hook-loading')).not.toBeInTheDocument();
      });

      // Save value
      act(() => {
        screen.getByTestId('save-button').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('hook-value')).toHaveTextContent('hook_value');
      });

      // Load value
      act(() => {
        screen.getByTestId('load-button').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('hook-value')).toHaveTextContent('hook_value');
      });
    });

    it('should handle hook errors gracefully', async () => {
      // Mock storage to throw error
      const mockError = new Error('Hook storage error');
      jest.spyOn(console, 'error').mockImplementation(() => {});
      
      const originalGetRepositoryStorage = require('../../index').getRepositoryStorage;
      jest.spyOn(require('../../index'), 'getRepositoryStorage').mockRejectedValue(mockError);

      render(<HookTestComponent repositoryName="hook_error" />);

      await waitFor(() => {
        expect(screen.getByTestId('hook-error')).toHaveTextContent('Hook storage error');
      });

      // Restore
      require('../../index').getRepositoryStorage = originalGetRepositoryStorage;
      jest.restoreAllMocks();
    });
  });

  describe('React Lifecycle Integration', () => {
    it('should handle component mounting and unmounting', async () => {
      const { unmount } = render(<StorageTestComponent repositoryName="lifecycle_test" />);

      await waitFor(() => {
        expect(screen.getByTestId('storage-platform')).toHaveTextContent('browser');
      });

      // Unmount should not cause errors
      unmount();
    });

    it('should handle multiple component instances', async () => {
      render(
        <div>
          <StorageTestComponent repositoryName="multi_test_1" />
          <StorageTestComponent repositoryName="multi_test_2" />
        </div>
      );

      await waitFor(() => {
        const platforms = screen.getAllByTestId('storage-platform');
        expect(platforms).toHaveLength(2);
        platforms.forEach(platform => {
          expect(platform).toHaveTextContent('browser');
        });
      });
    });

    it('should handle rapid component re-renders', async () => {
      const TestWrapper: React.FC = () => {
        const [key, setKey] = useState(0);
        
        useEffect(() => {
          const interval = setInterval(() => {
            setKey(k => k + 1);
          }, 100);
          
          setTimeout(() => clearInterval(interval), 500);
          
          return () => clearInterval(interval);
        }, []);

        return <StorageTestComponent key={key} repositoryName="rerender_test" />;
      };

      render(<TestWrapper />);

      // Should eventually stabilize
      await waitFor(() => {
        expect(screen.getByTestId('storage-platform')).toHaveTextContent('browser');
      }, { timeout: 1000 });
    });
  });

  describe('React State Management Integration', () => {
    it('should integrate with React state', async () => {
      const StateIntegrationComponent: React.FC = () => {
        const [items, setItems] = useState<string[]>([]);
        const [storage, setStorage] = useState<IStorageAdapter | null>(null);

        useEffect(() => {
          const initStorage = async () => {
            await initializeStorage();
            const storageInstance = await getRepositoryStorage('state_integration');
            setStorage(storageInstance);

            // Load existing items
            const storedItems = await storageInstance.getItem('items') || [];
            setItems((storedItems as string[]) || []);
          };

          initStorage();
        }, []);

        const addItem = async (item: string) => {
          const newItems = [...items, item];
          setItems(newItems);
          if (storage) {
            await storage.setItem('items', newItems);
          }
        };

        return (
          <div>
            <div data-testid="item-count">{items.length}</div>
            <button 
              data-testid="add-item" 
              onClick={() => addItem(`item_${items.length}`)}
            >
              Add Item
            </button>
            {items.map((item, index) => (
              <div key={index} data-testid={`item-${index}`}>{item}</div>
            ))}
          </div>
        );
      };

      render(<StateIntegrationComponent />);

      // Add items
      for (let i = 0; i < 3; i++) {
        act(() => {
          screen.getByTestId('add-item').click();
        });
      }

      await waitFor(() => {
        expect(screen.getByTestId('item-count')).toHaveTextContent('3');
        expect(screen.getByTestId('item-0')).toHaveTextContent('item_0');
        expect(screen.getByTestId('item-1')).toHaveTextContent('item_1');
        expect(screen.getByTestId('item-2')).toHaveTextContent('item_2');
      });
    });

    it('should handle async state updates', async () => {
      const AsyncStateComponent: React.FC = () => {
        const [data, setData] = useState<any>(null);
        const [loading, setLoading] = useState(true);

        useEffect(() => {
          const loadData = async () => {
            await initializeStorage();
            const storage = await getRepositoryStorage('async_state');
            
            // Simulate async data loading
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const storedData = await storage.getItem('async_data') || { message: 'default' };
            setData(storedData);
            setLoading(false);
          };

          loadData();
        }, []);

        if (loading) return <div data-testid="async-loading">Loading...</div>;

        return <div data-testid="async-data">{data.message}</div>;
      };

      render(<AsyncStateComponent />);

      expect(screen.getByTestId('async-loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('async-data')).toHaveTextContent('default');
      });
    });
  });

  describe('React Performance Considerations', () => {
    it('should not cause unnecessary re-renders', async () => {
      let renderCount = 0;

      const PerformanceTestComponent: React.FC = () => {
        renderCount++;
        const [, setStorage] = useState<IStorageAdapter | null>(null);

        useEffect(() => {
          const initStorage = async () => {
            await initializeStorage();
            const storageInstance = await getRepositoryStorage('performance_test');
            setStorage(storageInstance);
          };

          initStorage();
        }, []);

        return (
          <div data-testid="render-count">{renderCount}</div>
        );
      };

      render(<PerformanceTestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('render-count')).toBeInTheDocument();
      });

      // Should not re-render excessively
      expect(renderCount).toBeLessThanOrEqual(3);
    });

    it('should handle large datasets efficiently', async () => {
      const LargeDataComponent: React.FC = () => {
        const [items, setItems] = useState<any[]>([]);
        const [loading, setLoading] = useState(true);

        useEffect(() => {
          const loadLargeData = async () => {
            await initializeStorage();
            const storage = await getRepositoryStorage('large_data');
            
            const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
              id: i,
              name: `Item ${i}`,
              data: `Data for item ${i}`
            }));

            await storage.setItem('large_dataset', largeDataset);
            const retrieved = await storage.getItem('large_dataset');
            
            setItems((retrieved as any[]) || []);
            setLoading(false);
          };

          loadLargeData();
        }, []);

        if (loading) return <div data-testid="large-loading">Loading...</div>;

        return (
          <div>
            <div data-testid="large-count">{items.length}</div>
            <div data-testid="first-item">{items[0]?.name}</div>
            <div data-testid="last-item">{items[items.length - 1]?.name}</div>
          </div>
        );
      };

      render(<LargeDataComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('large-count')).toHaveTextContent('1000');
        expect(screen.getByTestId('first-item')).toHaveTextContent('Item 0');
        expect(screen.getByTestId('last-item')).toHaveTextContent('Item 999');
      }, { timeout: 5000 });
    });
  });

  describe('React Error Boundaries Integration', () => {
    it('should work with React error boundaries', async () => {
      class ErrorBoundary extends React.Component<
        { children: React.ReactNode },
        { hasError: boolean; error?: Error }
      > {
        constructor(props: { children: React.ReactNode }) {
          super(props);
          this.state = { hasError: false };
        }

        static getDerivedStateFromError(error: Error) {
          return { hasError: true, error };
        }

        render() {
          if (this.state.hasError) {
            return <div data-testid="error-boundary">Error caught</div>;
          }

          return this.props.children;
        }
      }

      const ErrorComponent: React.FC = () => {
        const [shouldError, setShouldError] = useState(false);

        useEffect(() => {
          if (shouldError) {
            throw new Error('Test error');
          }
        }, [shouldError]);

        return (
          <div>
            <StorageTestComponent repositoryName="error_boundary_test" />
            <button 
              data-testid="trigger-error" 
              onClick={() => setShouldError(true)}
            >
              Trigger Error
            </button>
          </div>
        );
      };

      render(
        <ErrorBoundary>
          <ErrorComponent />
        </ErrorBoundary>
      );

      // Should render normally first
      await waitFor(() => {
        expect(screen.getByTestId('storage-platform')).toHaveTextContent('browser');
      });

      // Trigger error
      act(() => {
        screen.getByTestId('trigger-error').click();
      });

      // Error boundary should catch it
      await waitFor(() => {
        expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      });
    });
  });
});
