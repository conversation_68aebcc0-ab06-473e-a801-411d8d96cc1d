/**
 * Comprehensive tests for MemoryStorageAdapter
 * Tests in-memory storage functionality including size limits and performance
 */

import { MemoryStorageAdapter } from '../../../adapters/MemoryStorageAdapter';
import { testBasicOperations, testTTLFunctionality, assertAdapterCapabilities, testData, wait } from '../../utils/test-helpers';

describe('MemoryStorageAdapter', () => {
  let adapter: MemoryStorageAdapter;

  beforeEach(async () => {
    adapter = new MemoryStorageAdapter();
    await adapter.initialize({
      name: 'test_memory_db',
      storeName: 'test_memory_store'
    });
  });

  afterEach(async () => {
    if (adapter) {
      await adapter.dispose();
    }
  });

  describe('Initialization', () => {
    it('should initialize with default configuration', async () => {
      const newAdapter = new MemoryStorageAdapter();
      await newAdapter.initialize({
        name: 'default_memory_db',
        storeName: 'default_memory_store'
      });

      expect(newAdapter).toBeDefined();
      const isHealthy = await newAdapter.isHealthy();
      expect(isHealthy).toBe(true);

      await newAdapter.dispose();
    });

    it('should initialize with custom max size', async () => {
      const customMaxSize = 5 * 1024 * 1024; // 5MB
      const newAdapter = new MemoryStorageAdapter(customMaxSize);
      await newAdapter.initialize({
        name: 'custom_memory_db',
        storeName: 'custom_memory_store'
      });

      const capabilities = newAdapter.getCapabilities();
      expect(capabilities.maxStorageSize).toBe(customMaxSize);

      await newAdapter.dispose();
    });

    it('should initialize with max size from config', async () => {
      const newAdapter = new MemoryStorageAdapter();
      await newAdapter.initialize({
        name: 'config_memory_db',
        storeName: 'config_memory_store',
        platformOptions: {
          maxSize: 1024 * 1024 // 1MB
        }
      });

      const capabilities = newAdapter.getCapabilities();
      expect(capabilities.maxStorageSize).toBe(1024 * 1024);

      await newAdapter.dispose();
    });

    it('should clear existing data on initialization', async () => {
      const newAdapter = new MemoryStorageAdapter();
      
      // Add some data before initialization
      newAdapter['store'].set('pre_init_key', {
        data: 'pre_init_value',
        timestamp: Date.now()
      });

      await newAdapter.initialize({
        name: 'clear_memory_db',
        storeName: 'clear_memory_store'
      });

      // Data should be cleared
      const length = await newAdapter.length();
      expect(length).toBe(0);

      await newAdapter.dispose();
    });
  });

  describe('Basic Operations', () => {
    it('should perform all basic storage operations', async () => {
      await testBasicOperations(adapter);
    });

    it('should handle different data types correctly', async () => {
      // Test simple data types
      for (const [key, value] of Object.entries(testData.simple)) {
        await adapter.setItem(key, value);
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toEqual(value);
      }

      // Test complex data types
      for (const [key, value] of Object.entries(testData.complex)) {
        await adapter.setItem(key, value);
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toEqual(value);
      }
    });

    it('should handle large datasets within memory limits', async () => {
      await adapter.setItem('large_dataset', testData.large);
      const retrieved = await adapter.getItem('large_dataset');
      expect(retrieved).toEqual(testData.large);
    });

    it('should maintain data integrity across operations', async () => {
      const testItems = {
        'item1': { type: 'object', value: { nested: 'data' } },
        'item2': { type: 'array', value: [1, 2, 3, 4, 5] },
        'item3': { type: 'string', value: 'simple string' },
        'item4': { type: 'number', value: 42.5 },
        'item5': { type: 'boolean', value: true }
      };

      // Set all items
      for (const [key, item] of Object.entries(testItems)) {
        await adapter.setItem(key, item);
      }

      // Verify all items
      for (const [key, expectedItem] of Object.entries(testItems)) {
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toEqual(expectedItem);
      }

      // Verify keys and length
      const keys = await adapter.keys();
      expect(keys).toHaveLength(5);
      expect(keys.sort()).toEqual(Object.keys(testItems).sort());
    });
  });

  describe('TTL Functionality', () => {
    it('should handle TTL correctly', async () => {
      await testTTLFunctionality(adapter);
    });

    it('should cleanup expired items efficiently', async () => {
      // Add items with different TTLs
      await adapter.setItem('short_ttl_1', 'value1', 50);
      await adapter.setItem('short_ttl_2', 'value2', 50);
      await adapter.setItem('medium_ttl', 'value3', 150);
      await adapter.setItem('no_ttl', 'value4');

      // Wait for short TTLs to expire
      await wait(75);

      // Cleanup should remove expired items
      const cleanedCount = await adapter.cleanup();
      expect(cleanedCount).toBe(2);

      // Check that only non-expired items remain
      expect(await adapter.getItem('short_ttl_1')).toBeNull();
      expect(await adapter.getItem('short_ttl_2')).toBeNull();
      expect(await adapter.getItem('medium_ttl')).toBe('value3');
      expect(await adapter.getItem('no_ttl')).toBe('value4');

      const remainingLength = await adapter.length();
      expect(remainingLength).toBe(2);
    });

    it('should handle TTL edge cases', async () => {
      // Zero TTL should not expire
      await adapter.setItem('zero_ttl', 'value', 0);
      await wait(100);
      expect(await adapter.getItem('zero_ttl')).toBe('value');

      // Negative TTL should not expire
      await adapter.setItem('negative_ttl', 'value', -1);
      await wait(100);
      expect(await adapter.getItem('negative_ttl')).toBe('value');

      // Very large TTL should not expire quickly
      await adapter.setItem('large_ttl', 'value', Number.MAX_SAFE_INTEGER);
      await wait(100);
      expect(await adapter.getItem('large_ttl')).toBe('value');
    });
  });

  describe('Memory Management', () => {
    it('should enforce storage size limits', async () => {
      const smallAdapter = new MemoryStorageAdapter(1024); // 1KB limit
      await smallAdapter.initialize({
        name: 'small_memory_db',
        storeName: 'small_memory_store'
      });

      // Try to store data that exceeds the limit
      const largeData = 'x'.repeat(2048); // 2KB of data
      
      await expect(smallAdapter.setItem('large_item', largeData)).rejects.toThrow('Storage quota exceeded');

      await smallAdapter.dispose();
    });

    it('should account for existing data when checking size limits', async () => {
      const limitedAdapter = new MemoryStorageAdapter(2048); // 2KB limit
      await limitedAdapter.initialize({
        name: 'limited_memory_db',
        storeName: 'limited_memory_store'
      });

      // Add some data
      await limitedAdapter.setItem('item1', 'x'.repeat(1000)); // ~1KB

      // Try to add more data that would exceed the limit
      await expect(limitedAdapter.setItem('item2', 'y'.repeat(1500))).rejects.toThrow('Storage quota exceeded');

      // But smaller data should work
      await limitedAdapter.setItem('item2', 'y'.repeat(500));
      expect(await limitedAdapter.getItem('item2')).toBe('y'.repeat(500));

      await limitedAdapter.dispose();
    });

    it('should handle size limit updates correctly', async () => {
      const limitedAdapter = new MemoryStorageAdapter(1024);
      await limitedAdapter.initialize({
        name: 'update_memory_db',
        storeName: 'update_memory_store'
      });

      // Add some data
      await limitedAdapter.setItem('item1', 'x'.repeat(500));

      // Update an existing item with larger data
      await limitedAdapter.setItem('item1', 'y'.repeat(800));
      expect(await limitedAdapter.getItem('item1')).toBe('y'.repeat(800));

      await limitedAdapter.dispose();
    });

    it('should calculate storage size accurately', async () => {
      const sizeTrackingAdapter = new MemoryStorageAdapter();
      await sizeTrackingAdapter.initialize({
        name: 'size_tracking_db',
        storeName: 'size_tracking_store'
      });

      // Add known data sizes
      await sizeTrackingAdapter.setItem('small', 'test'); // ~4 bytes
      await sizeTrackingAdapter.setItem('medium', 'x'.repeat(100)); // ~100 bytes
      await sizeTrackingAdapter.setItem('large', 'y'.repeat(1000)); // ~1000 bytes

      // The adapter should track size internally
      const currentSize = sizeTrackingAdapter['getCurrentSize']();
      expect(currentSize).toBeGreaterThan(1000);

      await sizeTrackingAdapter.dispose();
    });
  });

  describe('Storage Capabilities', () => {
    it('should report correct capabilities', () => {
      assertAdapterCapabilities(adapter, 'memory', false);
      
      const capabilities = adapter.getCapabilities();
      expect(capabilities.supportsBulkOperations).toBe(true);
      expect(capabilities.supportsTransactions).toBe(false);
      expect(capabilities.maxStorageSize).toBeGreaterThan(0);
    });

    it('should report custom max size in capabilities', () => {
      const customSize = 5 * 1024 * 1024;
      const customAdapter = new MemoryStorageAdapter(customSize);
      
      const capabilities = customAdapter.getCapabilities();
      expect(capabilities.maxStorageSize).toBe(customSize);
    });
  });

  describe('Health Monitoring', () => {
    it('should report healthy status when functioning', async () => {
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should report unhealthy status when disposed', async () => {
      await adapter.dispose();
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should always be healthy when initialized', async () => {
      // Memory adapter should always be healthy unless disposed
      for (let i = 0; i < 10; i++) {
        const isHealthy = await adapter.isHealthy();
        expect(isHealthy).toBe(true);
      }
    });
  });

  describe('Performance', () => {
    it('should handle high-frequency operations efficiently', async () => {
      const startTime = Date.now();
      
      // Perform many operations quickly
      const operations = Array.from({ length: 1000 }, async (_, i) => {
        await adapter.setItem(`perf_${i}`, `value_${i}`);
        return adapter.getItem(`perf_${i}`);
      });

      const results = await Promise.all(operations);
      const endTime = Date.now();

      // All operations should complete successfully
      results.forEach((result, i) => {
        expect(result).toBe(`value_${i}`);
      });

      // Should complete reasonably quickly (less than 1 second for 1000 operations)
      expect(endTime - startTime).toBeLessThan(1000);
    });

    it('should handle concurrent operations without data corruption', async () => {
      const concurrentOperations = Array.from({ length: 100 }, async (_, i) => {
        const key = `concurrent_${i}`;
        const value = `value_${i}`;
        
        await adapter.setItem(key, value);
        const retrieved = await adapter.getItem(key);
        
        return { key, expected: value, actual: retrieved };
      });

      const results = await Promise.all(concurrentOperations);
      
      // All operations should have correct results
      results.forEach(({ key, expected, actual }) => {
        expect(actual).toBe(expected);
      });

      // Final length should match number of operations
      const finalLength = await adapter.length();
      expect(finalLength).toBe(100);
    });

    it('should handle large number of keys efficiently', async () => {
      const keyCount = 10000;
      
      // Add many keys
      const setOperations = Array.from({ length: keyCount }, (_, i) =>
        adapter.setItem(`key_${i}`, `value_${i}`)
      );
      await Promise.all(setOperations);

      // Verify length
      const length = await adapter.length();
      expect(length).toBe(keyCount);

      // Verify keys operation is efficient
      const startTime = Date.now();
      const keys = await adapter.keys();
      const endTime = Date.now();

      expect(keys).toHaveLength(keyCount);
      expect(endTime - startTime).toBeLessThan(100); // Should be very fast for memory operations
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid keys gracefully', async () => {
      const invalidKeys = ['', null, undefined];
      
      for (const key of invalidKeys) {
        try {
          await adapter.setItem(key as any, 'value');
          const result = await adapter.getItem(key as any);
          // Should either work or return null
          expect(result === null || result === 'value').toBe(true);
        } catch (error) {
          // If it throws, should be meaningful error
          expect(error).toBeInstanceOf(Error);
        }
      }
    });

    it('should handle operations after disposal', async () => {
      await adapter.dispose();
      
      await expect(adapter.getItem('test')).rejects.toThrow('Storage adapter not initialized');
      await expect(adapter.setItem('test', 'value')).rejects.toThrow('Storage adapter not initialized');
      await expect(adapter.removeItem('test')).rejects.toThrow('Storage adapter not initialized');
      await expect(adapter.clear()).rejects.toThrow('Storage adapter not initialized');
      await expect(adapter.keys()).rejects.toThrow('Storage adapter not initialized');
      await expect(adapter.length()).rejects.toThrow('Storage adapter not initialized');
    });

    it('should handle circular references in data', async () => {
      const circularObject: any = { name: 'test' };
      circularObject.self = circularObject;
      
      // Should handle circular references gracefully (might throw or handle it)
      try {
        await adapter.setItem('circular', circularObject);
        // If it succeeds, verify we can retrieve something
        const retrieved = await adapter.getItem('circular');
        expect(retrieved).toBeDefined();
      } catch (error) {
        // If it fails, should be a meaningful error
        expect(error).toBeInstanceOf(Error);
      }
    });
  });

  describe('Memory Cleanup', () => {
    it('should dispose resources properly', async () => {
      // Add some data
      await adapter.setItem('dispose_test', 'test_value');
      expect(await adapter.length()).toBe(1);

      // Dispose
      await adapter.dispose();
      
      // Store should be cleared
      expect(adapter['store'].size).toBe(0);
      
      // Health check should return false
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should handle multiple dispose calls', async () => {
      await adapter.dispose();
      await adapter.dispose(); // Should not throw
      
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should clear all data on clear operation', async () => {
      // Add multiple items
      await adapter.setItem('clear1', 'value1');
      await adapter.setItem('clear2', 'value2');
      await adapter.setItem('clear3', 'value3');
      
      expect(await adapter.length()).toBe(3);
      
      // Clear all
      await adapter.clear();
      
      expect(await adapter.length()).toBe(0);
      expect(adapter['store'].size).toBe(0);
    });
  });
});
