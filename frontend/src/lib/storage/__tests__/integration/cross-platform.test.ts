/**
 * Cross-Platform Integration Tests
 * Tests storage functionality across different platforms and adapters
 */

import { StorageAdapterFactory } from '../../StorageFactory';
import { StorageManager } from '../../StorageManager';
import { MemoryStorageAdapter } from '../../adapters/MemoryStorageAdapter';
import { mockBrowserEnvironment, mockNodeEnvironment, mockReactNativeEnvironment, testData, wait } from '../utils/test-helpers';

// Mock platform detection to avoid conflicts
jest.mock('../../platformDetection', () => ({
  ...jest.requireActual('../../platformDetection'),
  detectPlatform: jest.fn(),
  isBrowser: jest.fn(),
  isNode: jest.fn(),
  isReactNative: jest.fn(),
  getRecommendedStorageAdapter: jest.fn()
}));

// Mock platform-specific modules
jest.mock('localforage', () => {
  const mockStore = new Map();
  return {
    createInstance: jest.fn(() => ({
      ready: jest.fn().mockResolvedValue(undefined),
      getItem: jest.fn((key) => Promise.resolve(mockStore.get(key) || null)),
      setItem: jest.fn((key, value) => {
        mockStore.set(key, value);
        return Promise.resolve(value);
      }),
      removeItem: jest.fn((key) => {
        mockStore.delete(key);
        return Promise.resolve();
      }),
      clear: jest.fn(() => {
        mockStore.clear();
        return Promise.resolve();
      }),
      keys: jest.fn(() => Promise.resolve(Array.from(mockStore.keys()))),
      length: jest.fn(() => Promise.resolve(mockStore.size)),
      driver: jest.fn(() => 'asyncStorage')
    })),
    INDEXEDDB: 'asyncStorage',
    LOCALSTORAGE: 'localStorageWrapper',
    WEBSQL: 'webSQLStorage'
  };
});

describe('Cross-Platform Integration Tests', () => {
  afterEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  describe('Platform Adapter Consistency', () => {
    it('should provide consistent API across all adapters', async () => {
      const adapters = [
        { name: 'memory', adapter: new MemoryStorageAdapter() }
      ];

      for (const { name, adapter } of adapters) {
        await adapter.initialize({
          name: `${name}_consistency_db`,
          storeName: `${name}_consistency_store`
        });

        // Test basic operations
        await adapter.setItem('test_key', 'test_value');
        const value = await adapter.getItem('test_key');
        expect(value).toBe('test_value');

        // Test complex data
        const complexData = { nested: { value: 42 }, array: [1, 2, 3] };
        await adapter.setItem('complex_key', complexData);
        const retrievedComplex = await adapter.getItem('complex_key');
        expect(retrievedComplex).toEqual(complexData);

        // Test TTL
        await adapter.setItem('ttl_key', 'ttl_value', 100);
        expect(await adapter.getItem('ttl_key')).toBe('ttl_value');
        await wait(150);
        expect(await adapter.getItem('ttl_key')).toBeNull();

        // Test metadata operations
        await adapter.setItem('meta1', 'value1');
        await adapter.setItem('meta2', 'value2');
        const keys = await adapter.keys();
        expect(keys).toContain('meta1');
        expect(keys).toContain('meta2');

        const length = await adapter.length();
        expect(length).toBeGreaterThanOrEqual(2);

        // Test capabilities
        const capabilities = adapter.getCapabilities();
        expect(capabilities).toHaveProperty('platform');
        expect(capabilities).toHaveProperty('persistent');
        expect(capabilities).toHaveProperty('supportsTTL');
        expect(capabilities.supportsTTL).toBe(true);

        // Test health
        const isHealthy = await adapter.isHealthy();
        expect(isHealthy).toBe(true);

        await adapter.dispose();
      }
    });

    it('should handle data serialization consistently', async () => {
      const testCases = [
        { key: 'string', value: 'test string' },
        { key: 'number', value: 42 },
        { key: 'boolean', value: true },
        { key: 'null', value: null },
        { key: 'array', value: [1, 2, 3, 'four'] },
        { key: 'object', value: { nested: { deep: 'value' } } },
        { key: 'date', value: new Date().toISOString() },
        { key: 'mixed', value: { str: 'test', num: 123, bool: false, arr: [1, 2] } }
      ];

      const adapter = new MemoryStorageAdapter();
      await adapter.initialize({
        name: 'serialization_test_db',
        storeName: 'serialization_test_store'
      });

      for (const testCase of testCases) {
        await adapter.setItem(testCase.key, testCase.value);
        const retrieved = await adapter.getItem(testCase.key);
        expect(retrieved).toEqual(testCase.value);
      }

      await adapter.dispose();
    });

    it('should handle errors consistently across adapters', async () => {
      const adapter = new MemoryStorageAdapter();
      await adapter.initialize({
        name: 'error_test_db',
        storeName: 'error_test_store'
      });

      // Test operations after disposal
      await adapter.dispose();

      await expect(adapter.getItem('test')).rejects.toThrow('Storage adapter not initialized');
      await expect(adapter.setItem('test', 'value')).rejects.toThrow('Storage adapter not initialized');
      await expect(adapter.removeItem('test')).rejects.toThrow('Storage adapter not initialized');
      await expect(adapter.clear()).rejects.toThrow('Storage adapter not initialized');
      await expect(adapter.keys()).rejects.toThrow('Storage adapter not initialized');
      await expect(adapter.length()).rejects.toThrow('Storage adapter not initialized');

      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });
  });

  describe('Storage Factory Cross-Platform', () => {
    it('should create appropriate adapters for different environments', async () => {
      // Test browser environment
      const cleanupBrowser = mockBrowserEnvironment();
      try {
        const browserFactory = StorageAdapterFactory.getInstance();
        expect(browserFactory.getAdapterType()).toBe('browser');
        expect(browserFactory.isAdapterAvailable('browser')).toBe(true);
        expect(browserFactory.isAdapterAvailable('memory')).toBe(true);
      } finally {
        cleanupBrowser();
      }

      // Test Node.js environment
      const cleanupNode = mockNodeEnvironment();
      try {
        // Reset singleton to get new instance for different environment
        (StorageAdapterFactory as any).instance = null;
        const nodeFactory = StorageAdapterFactory.getInstance();
        expect(nodeFactory.getAdapterType()).toBe('node');
        expect(nodeFactory.isAdapterAvailable('node')).toBe(true);
        expect(nodeFactory.isAdapterAvailable('memory')).toBe(true);
      } finally {
        cleanupNode();
      }

      // Test React Native environment
      const cleanupRN = mockReactNativeEnvironment();
      try {
        (StorageAdapterFactory as any).instance = null;
        const rnFactory = StorageAdapterFactory.getInstance();
        expect(rnFactory.getAdapterType()).toBe('react-native');
        expect(rnFactory.isAdapterAvailable('react-native')).toBe(true);
        expect(rnFactory.isAdapterAvailable('memory')).toBe(true);
      } finally {
        cleanupRN();
      }

      // Reset singleton
      (StorageAdapterFactory as any).instance = null;
    });

    it('should handle fallback chains correctly', async () => {
      const cleanupBrowser = mockBrowserEnvironment();
      try {
        const factory = StorageAdapterFactory.getInstance();
        
        // Test fallback from unavailable to available adapter
        const adapter = await factory.createAdapterWithFallback({
          name: 'fallback_test_db',
          storeName: 'fallback_test_store'
        }, ['fake-adapter', 'memory']);

        expect(adapter).toBeDefined();
        expect(adapter.getCapabilities().platform).toBe('memory');

        await adapter.dispose();
      } finally {
        cleanupBrowser();
      }
    });
  });

  describe('Storage Manager Cross-Platform', () => {
    it('should manage multiple adapters across platforms', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'multi_platform_db',
        version: 1
      });

      // Create storage instances
      const storage1 = await manager.getStorage('store1');
      const storage2 = await manager.getStorage('store2');
      const storage3 = await manager.getStorage('store3');

      expect(storage1).toBeDefined();
      expect(storage2).toBeDefined();
      expect(storage3).toBeDefined();

      // Test that they work independently
      await storage1.setItem('key', 'value1');
      await storage2.setItem('key', 'value2');
      await storage3.setItem('key', 'value3');

      expect(await storage1.getItem('key')).toBe('value1');
      expect(await storage2.getItem('key')).toBe('value2');
      expect(await storage3.getItem('key')).toBe('value3');

      // Test health monitoring
      const healthStatus = await manager.getHealthStatus();
      expect(healthStatus.healthy).toBe(true);
      expect(healthStatus.adapters).toHaveLength(3);

      await manager.cleanup();
    });

    it('should handle global cleanup across adapters', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'cleanup_test_db',
        version: 1
      });

      const storage1 = await manager.getStorage('cleanup_store1');
      const storage2 = await manager.getStorage('cleanup_store2');

      // Add items with TTL
      await storage1.setItem('ttl1', 'value1', 100);
      await storage1.setItem('ttl2', 'value2', 100);
      await storage2.setItem('ttl3', 'value3', 100);
      await storage2.setItem('ttl4', 'value4', 100);

      // Wait for expiration
      await wait(150);

      // Global cleanup should remove expired items from all stores
      const cleanupResults = await manager.performGlobalCleanup();
      
      expect(cleanupResults).toHaveProperty('cleanup_store1');
      expect(cleanupResults).toHaveProperty('cleanup_store2');
      expect(cleanupResults['cleanup_store1']).toBeGreaterThanOrEqual(2);
      expect(cleanupResults['cleanup_store2']).toBeGreaterThanOrEqual(2);

      await manager.cleanup();
    });
  });

  describe('Data Migration and Compatibility', () => {
    it('should handle data format compatibility across adapters', async () => {
      // Create data with one adapter
      const sourceAdapter = new MemoryStorageAdapter();
      await sourceAdapter.initialize({
        name: 'source_db',
        storeName: 'source_store'
      });

      const testData = {
        user: { id: 1, name: 'John Doe', preferences: { theme: 'dark' } },
        session: { token: 'abc123', expires: Date.now() + 3600000 },
        cache: { data: [1, 2, 3, 4, 5], timestamp: Date.now() }
      };

      for (const [key, value] of Object.entries(testData)) {
        await sourceAdapter.setItem(key, value);
      }

      // Simulate data export/import
      const exportedData: Record<string, any> = {};
      const keys = await sourceAdapter.keys();
      for (const key of keys) {
        exportedData[key] = await sourceAdapter.getItem(key);
      }

      // Import to another adapter
      const targetAdapter = new MemoryStorageAdapter();
      await targetAdapter.initialize({
        name: 'target_db',
        storeName: 'target_store'
      });

      for (const [key, value] of Object.entries(exportedData)) {
        await targetAdapter.setItem(key, value);
      }

      // Verify data integrity
      for (const [key, expectedValue] of Object.entries(testData)) {
        const retrievedValue = await targetAdapter.getItem(key);
        expect(retrievedValue).toEqual(expectedValue);
      }

      await sourceAdapter.dispose();
      await targetAdapter.dispose();
    });

    it('should handle version compatibility', async () => {
      const adapter = new MemoryStorageAdapter();
      
      // Initialize with version 1
      await adapter.initialize({
        name: 'version_test_db',
        storeName: 'version_test_store',
        version: 1
      });

      await adapter.setItem('version_data', { version: 1, data: 'v1_data' });
      
      // Simulate version upgrade
      await adapter.dispose();
      await adapter.initialize({
        name: 'version_test_db',
        storeName: 'version_test_store',
        version: 2
      });

      // Data should still be accessible
      const data = await adapter.getItem('version_data');
      expect(data).toEqual({ version: 1, data: 'v1_data' });

      await adapter.dispose();
    });
  });

  describe('Performance Across Platforms', () => {
    it('should handle concurrent operations across adapters', async () => {
      const adapters = [
        new MemoryStorageAdapter(),
        new MemoryStorageAdapter(),
        new MemoryStorageAdapter()
      ];

      // Initialize all adapters
      for (let i = 0; i < adapters.length; i++) {
        await adapters[i].initialize({
          name: `concurrent_db_${i}`,
          storeName: `concurrent_store_${i}`
        });
      }

      // Perform concurrent operations across adapters
      const operations = adapters.flatMap((adapter, adapterIndex) =>
        Array.from({ length: 10 }, async (_, opIndex) => {
          const key = `key_${adapterIndex}_${opIndex}`;
          const value = `value_${adapterIndex}_${opIndex}`;
          
          await adapter.setItem(key, value);
          return adapter.getItem(key);
        })
      );

      const results = await Promise.all(operations);

      // Verify all operations completed successfully
      results.forEach((result, index) => {
        const adapterIndex = Math.floor(index / 10);
        const opIndex = index % 10;
        const expectedValue = `value_${adapterIndex}_${opIndex}`;
        expect(result).toBe(expectedValue);
      });

      // Cleanup
      for (const adapter of adapters) {
        await adapter.dispose();
      }
    });

    it('should handle large datasets consistently', async () => {
      const adapter = new MemoryStorageAdapter();
      await adapter.initialize({
        name: 'large_data_db',
        storeName: 'large_data_store'
      });

      const largeDataset = testData.large; // 1000 items

      const startTime = Date.now();
      await adapter.setItem('large_dataset', largeDataset);
      const retrieved = await adapter.getItem('large_dataset');
      const endTime = Date.now();

      expect(retrieved).toEqual(largeDataset);
      expect(retrieved).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second

      await adapter.dispose();
    });
  });

  describe('Error Recovery Across Platforms', () => {
    it('should handle adapter failures gracefully', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'failure_test_db',
        version: 1
      });

      const workingStorage = await manager.getStorage('working_store');
      
      // Create a mock failing storage
      const failingStorage = {
        initialize: jest.fn().mockResolvedValue(undefined),
        setItem: jest.fn().mockRejectedValue(new Error('Storage failure')),
        getItem: jest.fn().mockRejectedValue(new Error('Storage failure')),
        isHealthy: jest.fn().mockResolvedValue(false),
        cleanup: jest.fn().mockResolvedValue(0),
        dispose: jest.fn().mockResolvedValue(undefined),
        getCapabilities: jest.fn().mockReturnValue({ platform: 'failing' })
      };

      // Add failing storage to manager
      manager['adapters'].set('failing_store', failingStorage as any);

      // Working storage should still work
      await workingStorage.setItem('test_key', 'test_value');
      expect(await workingStorage.getItem('test_key')).toBe('test_value');

      // Health status should reflect the failure
      const healthStatus = await manager.getHealthStatus();
      expect(healthStatus.healthy).toBe(false); // Overall health affected by failing adapter
      expect(healthStatus.adapters.some(a => a.healthy === false)).toBe(true);

      await manager.cleanup();
    });

    it('should recover from temporary failures', async () => {
      const adapter = new MemoryStorageAdapter();
      await adapter.initialize({
        name: 'recovery_test_db',
        storeName: 'recovery_test_store'
      });

      // Add some data
      await adapter.setItem('recovery_key', 'recovery_value');
      expect(await adapter.getItem('recovery_key')).toBe('recovery_value');

      // Simulate temporary failure by disposing and reinitializing
      await adapter.dispose();
      expect(await adapter.isHealthy()).toBe(false);

      // Reinitialize
      await adapter.initialize({
        name: 'recovery_test_db',
        storeName: 'recovery_test_store'
      });

      // Should be healthy again
      expect(await adapter.isHealthy()).toBe(true);

      // Can store new data
      await adapter.setItem('new_key', 'new_value');
      expect(await adapter.getItem('new_key')).toBe('new_value');

      await adapter.dispose();
    });
  });
});
