/**
 * Node.js Environment Tests
 * Tests storage functionality specifically in Node.js runtime environment
 */

import { StorageAdapterFactory } from '../../StorageFactory';
import { StorageManager } from '../../StorageManager';
import { detectPlatform, isNode, getRecommendedStorageAdapter } from '../../platformDetection';
import { mockNodeEnvironment, createMockFileSystem } from '../utils/test-helpers';

describe('Node.js Environment Tests', () => {
  let cleanupNodeMock: () => void;
  let mockFs: any;
  let mockPath: any;

  beforeEach(() => {
    cleanupNodeMock = mockNodeEnvironment();
    const mocks = createMockFileSystem();
    mockFs = mocks.mockFs;
    mockPath = mocks.mockPath;

    // Mock the dynamic imports for Node.js modules
    jest.doMock('fs', () => mockFs, { virtual: true });
    jest.doMock('path', () => mockPath, { virtual: true });
  });

  afterEach(() => {
    cleanupNodeMock();
    jest.clearAllMocks();
    jest.resetModules();
  });

  describe('Platform Detection in Node.js', () => {
    it('should correctly detect Node.js environment', () => {
      const isNodeEnv = isNode();
      expect(isNodeEnv).toBe(true);

      const platform = detectPlatform();
      expect(platform.platform).toBe('node');
      expect(platform.isServer).toBe(true);
      expect(platform.isBrowser).toBe(false);
      expect(platform.isReactNative).toBe(false);
    });

    it('should recommend Node.js adapter', () => {
      const recommendedAdapter = getRecommendedStorageAdapter();
      expect(recommendedAdapter).toBe('node');
    });

    it('should detect Node.js version information', () => {
      const platform = detectPlatform();
      expect(platform.details).toHaveProperty('nodeVersion');
      expect(platform.details.nodeVersion).toBe('18.0.0');
    });

    it('should detect environment variables', () => {
      process.env.NODE_ENV = 'test';
      process.env.CUSTOM_VAR = 'custom_value';

      const platform = detectPlatform();
      expect(platform.details).toHaveProperty('environment');
      expect(platform.details.environment).toBe('test');
    });
  });

  describe('Storage Factory in Node.js', () => {
    it('should create Node.js adapter by default', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapterType = factory.getAdapterType();
      expect(adapterType).toBe('node');
    });

    it('should create Node.js adapter when specifically requested', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      // Mock the NodeStorageAdapter import
      const mockNodeAdapter = {
        initialize: jest.fn().mockResolvedValue(undefined),
        dispose: jest.fn().mockResolvedValue(undefined),
        getCapabilities: jest.fn().mockReturnValue({
          platform: 'node',
          persistent: true,
          supportsTTL: true,
          supportsBulkOperations: false,
          maxStorageSize: -1
        })
      };

      jest.doMock('../../adapters/NodeStorageAdapter', () => ({
        NodeStorageAdapter: jest.fn(() => mockNodeAdapter)
      }), { virtual: true });

      const adapter = await factory.createSpecificAdapter('node', {
        name: 'test_node_db',
        storeName: 'test_node_store'
      });

      expect(adapter).toBeDefined();
      expect(mockNodeAdapter.initialize).toHaveBeenCalled();

      await adapter.dispose();
    });

    it('should report Node.js adapter as available', () => {
      const factory = StorageAdapterFactory.getInstance();
      const isAvailable = factory.isAdapterAvailable('node');
      expect(isAvailable).toBe(true);

      const availableTypes = factory.getAvailableAdapterTypes();
      expect(availableTypes).toContain('node');
    });
  });

  describe('Storage Manager in Node.js', () => {
    it('should initialize storage manager in Node.js environment', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'node_test_db',
        version: 1,
        description: 'Node.js test database'
      });

      expect(manager).toBeDefined();
      await manager.cleanup();
    });

    it('should create Node.js storage instances', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'node_storage_db',
        version: 1
      });

      // Mock the storage creation
      const mockStorage = {
        initialize: jest.fn().mockResolvedValue(undefined),
        dispose: jest.fn().mockResolvedValue(undefined),
        getCapabilities: jest.fn().mockReturnValue({
          platform: 'node',
          persistent: true
        }),
        isHealthy: jest.fn().mockResolvedValue(true)
      };

      jest.spyOn(manager['factory'], 'createAdapter').mockResolvedValue(mockStorage as any);

      const storage = await manager.getStorage('node_store');
      expect(storage).toBeDefined();
      expect(mockStorage.initialize).toHaveBeenCalled();

      await manager.cleanup();
    });

    it('should handle multiple Node.js storage instances', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'multi_node_db',
        version: 1
      });

      const mockStorage1 = {
        initialize: jest.fn().mockResolvedValue(undefined),
        dispose: jest.fn().mockResolvedValue(undefined),
        isHealthy: jest.fn().mockResolvedValue(true),
        cleanup: jest.fn().mockResolvedValue(0)
      };

      const mockStorage2 = {
        initialize: jest.fn().mockResolvedValue(undefined),
        dispose: jest.fn().mockResolvedValue(undefined),
        isHealthy: jest.fn().mockResolvedValue(true),
        cleanup: jest.fn().mockResolvedValue(0)
      };

      jest.spyOn(manager['factory'], 'createAdapter')
        .mockResolvedValueOnce(mockStorage1 as any)
        .mockResolvedValueOnce(mockStorage2 as any);

      const storage1 = await manager.getStorage('store1');
      const storage2 = await manager.getStorage('store2');

      expect(storage1).toBeDefined();
      expect(storage2).toBeDefined();
      expect(storage1).not.toBe(storage2);

      // Test health status
      const healthStatus = await manager.getHealthStatus();
      expect(healthStatus.healthy).toBe(true);
      expect(healthStatus.adapters).toHaveLength(2);

      await manager.cleanup();
    });
  });

  describe('File System Integration', () => {
    it('should handle file system operations correctly', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      // Mock successful file operations
      mockFs.promises.mkdir.mockResolvedValue(undefined);
      mockFs.promises.access.mockResolvedValue(undefined);
      mockFs.promises.readFile.mockResolvedValue('{}');
      mockFs.promises.writeFile.mockResolvedValue(undefined);

      const mockNodeAdapter = {
        initialize: jest.fn().mockResolvedValue(undefined),
        setItem: jest.fn().mockResolvedValue(undefined),
        getItem: jest.fn().mockResolvedValue('test_value'),
        dispose: jest.fn().mockResolvedValue(undefined)
      };

      jest.doMock('../../adapters/NodeStorageAdapter', () => ({
        NodeStorageAdapter: jest.fn(() => mockNodeAdapter)
      }), { virtual: true });

      const adapter = await factory.createSpecificAdapter('node', {
        name: 'fs_test_db',
        storeName: 'fs_test_store'
      });

      await adapter.setItem('test_key', 'test_value');
      const value = await adapter.getItem('test_key');

      expect(value).toBe('test_value');
      expect(mockNodeAdapter.setItem).toHaveBeenCalledWith('test_key', 'test_value');

      await adapter.dispose();
    });

    it('should handle file system errors gracefully', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      // Mock file system errors
      mockFs.promises.mkdir.mockRejectedValue(new Error('Permission denied'));

      const mockNodeAdapter = {
        initialize: jest.fn().mockRejectedValue(new Error('Failed to create storage directory: Permission denied'))
      };

      jest.doMock('../../adapters/NodeStorageAdapter', () => ({
        NodeStorageAdapter: jest.fn(() => mockNodeAdapter)
      }), { virtual: true });

      await expect(factory.createSpecificAdapter('node', {
        name: 'error_test_db',
        storeName: 'error_test_store'
      })).rejects.toThrow('Failed to create storage directory');
    });

    it('should handle different storage directories', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      const mockNodeAdapter = {
        initialize: jest.fn().mockResolvedValue(undefined),
        dispose: jest.fn().mockResolvedValue(undefined)
      };

      jest.doMock('../../adapters/NodeStorageAdapter', () => ({
        NodeStorageAdapter: jest.fn(() => mockNodeAdapter)
      }), { virtual: true });

      // Test with custom storage directory
      const adapter = await factory.createSpecificAdapter('node', {
        name: 'custom_dir_db',
        storeName: 'custom_dir_store',
        platformOptions: {
          storageDir: '/custom/storage/path'
        }
      });

      expect(mockNodeAdapter.initialize).toHaveBeenCalledWith({
        name: 'custom_dir_db',
        storeName: 'custom_dir_store',
        platformOptions: {
          storageDir: '/custom/storage/path'
        }
      });

      await adapter.dispose();
    });
  });

  describe('Environment Variables and Configuration', () => {
    it('should respect STORAGE_DIR environment variable', async () => {
      process.env.STORAGE_DIR = '/env/storage/directory';

      const factory = StorageAdapterFactory.getInstance();
      
      const mockNodeAdapter = {
        initialize: jest.fn().mockResolvedValue(undefined),
        dispose: jest.fn().mockResolvedValue(undefined)
      };

      jest.doMock('../../adapters/NodeStorageAdapter', () => ({
        NodeStorageAdapter: jest.fn(() => mockNodeAdapter)
      }), { virtual: true });

      const adapter = await factory.createSpecificAdapter('node', {
        name: 'env_test_db',
        storeName: 'env_test_store'
      });

      expect(mockNodeAdapter.initialize).toHaveBeenCalled();

      delete process.env.STORAGE_DIR;
      await adapter.dispose();
    });

    it('should handle NODE_ENV variations', async () => {
      const environments = ['development', 'production', 'test', 'staging'];

      for (const env of environments) {
        process.env.NODE_ENV = env;

        const platform = detectPlatform();
        expect(platform.details.environment).toBe(env);
      }

      delete process.env.NODE_ENV;
    });

    it('should detect process information correctly', () => {
      const platform = detectPlatform();
      
      expect(platform.details).toHaveProperty('nodeVersion');
      expect(platform.details).toHaveProperty('platform');
      expect(platform.details.platform).toBe('linux');
    });
  });

  describe('Performance in Node.js Environment', () => {
    it('should handle concurrent file operations', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      let operationCount = 0;
      const mockNodeAdapter = {
        initialize: jest.fn().mockResolvedValue(undefined),
        setItem: jest.fn().mockImplementation(async () => {
          operationCount++;
          return Promise.resolve();
        }),
        getItem: jest.fn().mockImplementation(async (key) => {
          return Promise.resolve(`value_for_${key}`);
        }),
        dispose: jest.fn().mockResolvedValue(undefined)
      };

      jest.doMock('../../adapters/NodeStorageAdapter', () => ({
        NodeStorageAdapter: jest.fn(() => mockNodeAdapter)
      }), { virtual: true });

      const adapter = await factory.createSpecificAdapter('node', {
        name: 'concurrent_test_db',
        storeName: 'concurrent_test_store'
      });

      // Perform concurrent operations
      const operations = Array.from({ length: 10 }, async (_, i) => {
        await adapter.setItem(`key_${i}`, `value_${i}`);
        return adapter.getItem(`key_${i}`);
      });

      const results = await Promise.all(operations);

      expect(operationCount).toBe(10);
      results.forEach((result, i) => {
        expect(result).toBe(`value_for_key_${i}`);
      });

      await adapter.dispose();
    });

    it('should handle large datasets efficiently', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      const mockNodeAdapter = {
        initialize: jest.fn().mockResolvedValue(undefined),
        setItem: jest.fn().mockResolvedValue(undefined),
        getItem: jest.fn().mockImplementation(async (key) => {
          if (key === 'large_dataset') {
            return Array.from({ length: 10000 }, (_, i) => ({ id: i, data: `item_${i}` }));
          }
          return null;
        }),
        dispose: jest.fn().mockResolvedValue(undefined)
      };

      jest.doMock('../../adapters/NodeStorageAdapter', () => ({
        NodeStorageAdapter: jest.fn(() => mockNodeAdapter)
      }), { virtual: true });

      const adapter = await factory.createSpecificAdapter('node', {
        name: 'large_data_db',
        storeName: 'large_data_store'
      });

      const largeDataset = Array.from({ length: 10000 }, (_, i) => ({ id: i, data: `item_${i}` }));
      
      const startTime = Date.now();
      await adapter.setItem('large_dataset', largeDataset);
      const retrieved = await adapter.getItem('large_dataset');
      const endTime = Date.now();

      expect(retrieved).toHaveLength(10000);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second

      await adapter.dispose();
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from temporary file system errors', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      let attemptCount = 0;
      const mockNodeAdapter = {
        initialize: jest.fn().mockResolvedValue(undefined),
        setItem: jest.fn().mockImplementation(async () => {
          attemptCount++;
          if (attemptCount === 1) {
            throw new Error('EBUSY: resource busy or locked');
          }
          return Promise.resolve();
        }),
        dispose: jest.fn().mockResolvedValue(undefined)
      };

      jest.doMock('../../adapters/NodeStorageAdapter', () => ({
        NodeStorageAdapter: jest.fn(() => mockNodeAdapter)
      }), { virtual: true });

      const adapter = await factory.createSpecificAdapter('node', {
        name: 'recovery_test_db',
        storeName: 'recovery_test_store'
      });

      // Should eventually succeed after retry
      await adapter.setItem('test_key', 'test_value');
      expect(attemptCount).toBeGreaterThan(1);

      await adapter.dispose();
    });

    it('should handle disk space issues gracefully', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      const mockNodeAdapter = {
        initialize: jest.fn().mockResolvedValue(undefined),
        setItem: jest.fn().mockRejectedValue(new Error('ENOSPC: no space left on device')),
        dispose: jest.fn().mockResolvedValue(undefined)
      };

      jest.doMock('../../adapters/NodeStorageAdapter', () => ({
        NodeStorageAdapter: jest.fn(() => mockNodeAdapter)
      }), { virtual: true });

      const adapter = await factory.createSpecificAdapter('node', {
        name: 'disk_space_db',
        storeName: 'disk_space_store'
      });

      await expect(adapter.setItem('test_key', 'test_value')).rejects.toThrow('ENOSPC');

      await adapter.dispose();
    });
  });
});
