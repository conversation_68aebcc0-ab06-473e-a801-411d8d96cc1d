/**
 * Comprehensive tests for NodeStorageAdapter
 * Tests Node.js-specific storage functionality including file system operations
 */

import { NodeStorageAdapter } from '../../../adapters/NodeStorageAdapter';
import { mockNodeEnvironment, createMockFileSystem, testBasicOperations, testTTLFunctionality, assertAdapterCapabilities, testData, wait } from '../../utils/test-helpers';

// Mock Node.js modules
const mockFs = {
  promises: {
    mkdir: jest.fn().mockResolvedValue(undefined),
    access: jest.fn().mockResolvedValue(undefined),
    readFile: jest.fn().mockResolvedValue('{}'),
    writeFile: jest.fn().mockResolvedValue(undefined),
    unlink: jest.fn().mockResolvedValue(undefined),
    readdir: jest.fn().mockResolvedValue([])
  }
};

const mockPath = {
  join: jest.fn((...args) => args.join('/')),
  dirname: jest.fn((path) => path.split('/').slice(0, -1).join('/')),
  basename: jest.fn((path) => path.split('/').pop())
};

// Mock the dynamic imports
jest.mock('fs', () => mockFs, { virtual: true });
jest.mock('path', () => mockPath, { virtual: true });

describe('NodeStorageAdapter', () => {
  let adapter: NodeStorageAdapter;
  let cleanupNodeMock: () => void;
  let mockStore: Record<string, any>;

  beforeEach(async () => {
    cleanupNodeMock = mockNodeEnvironment();
    mockStore = {};
    
    // Setup mock file system behavior
    mockFs.promises.readFile.mockImplementation(() => 
      Promise.resolve(JSON.stringify(mockStore))
    );
    
    mockFs.promises.writeFile.mockImplementation((path, data) => {
      mockStore = JSON.parse(data as string);
      return Promise.resolve();
    });

    adapter = new NodeStorageAdapter();
    await adapter.initialize({
      name: 'test_node_db',
      storeName: 'test_node_store'
    });
  });

  afterEach(async () => {
    if (adapter) {
      await adapter.dispose();
    }
    cleanupNodeMock();
    jest.clearAllMocks();
    mockStore = {};
  });

  describe('Initialization', () => {
    it('should initialize with default configuration', async () => {
      const newAdapter = new NodeStorageAdapter();
      await newAdapter.initialize({
        name: 'default_node_db',
        storeName: 'default_node_store'
      });

      expect(newAdapter).toBeDefined();
      expect(mockFs.promises.mkdir).toHaveBeenCalled();
      expect(mockPath.join).toHaveBeenCalled();

      await newAdapter.dispose();
    });

    it('should initialize with custom storage directory', async () => {
      const newAdapter = new NodeStorageAdapter();
      await newAdapter.initialize({
        name: 'custom_db',
        storeName: 'custom_store',
        platformOptions: {
          storageDir: '/custom/storage/path'
        }
      });

      expect(mockPath.join).toHaveBeenCalledWith('/custom/storage/path', 'custom_db');
      await newAdapter.dispose();
    });

    it('should create storage directory if it does not exist', async () => {
      mockFs.promises.mkdir.mockClear();
      
      const newAdapter = new NodeStorageAdapter();
      await newAdapter.initialize({
        name: 'new_db',
        storeName: 'new_store'
      });

      expect(mockFs.promises.mkdir).toHaveBeenCalledWith(
        expect.stringContaining('new_db'),
        { recursive: true }
      );

      await newAdapter.dispose();
    });

    it('should handle directory creation failure', async () => {
      mockFs.promises.mkdir.mockRejectedValue(new Error('Permission denied'));

      const newAdapter = new NodeStorageAdapter();
      await expect(newAdapter.initialize({
        name: 'failing_db',
        storeName: 'failing_store'
      })).rejects.toThrow('Failed to create storage directory');
    });

    it('should create store file if it does not exist', async () => {
      mockFs.promises.access.mockRejectedValue(new Error('File not found'));
      mockFs.promises.writeFile.mockClear();

      const newAdapter = new NodeStorageAdapter();
      await newAdapter.initialize({
        name: 'new_file_db',
        storeName: 'new_file_store'
      });

      expect(mockFs.promises.writeFile).toHaveBeenCalledWith(
        expect.stringContaining('.json'),
        '{}'
      );

      await newAdapter.dispose();
    });
  });

  describe('Basic Operations', () => {
    it('should perform all basic storage operations', async () => {
      await testBasicOperations(adapter);
    });

    it('should handle different data types correctly', async () => {
      // Test simple data types
      for (const [key, value] of Object.entries(testData.simple)) {
        await adapter.setItem(key, value);
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toEqual(value);
      }

      // Test complex data types
      for (const [key, value] of Object.entries(testData.complex)) {
        await adapter.setItem(key, value);
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toEqual(value);
      }
    });

    it('should persist data to file system', async () => {
      await adapter.setItem('persist_test', 'persist_value');
      
      // Verify writeFile was called
      expect(mockFs.promises.writeFile).toHaveBeenCalled();
      
      // Verify data is in mock store
      expect(mockStore).toHaveProperty('persist_test');
    });

    it('should handle large datasets', async () => {
      await adapter.setItem('large_dataset', testData.large);
      const retrieved = await adapter.getItem('large_dataset');
      expect(retrieved).toEqual(testData.large);
    });
  });

  describe('TTL Functionality', () => {
    it('should handle TTL correctly', async () => {
      await testTTLFunctionality(adapter);
    });

    it('should cleanup expired items', async () => {
      // Add items with different TTLs
      await adapter.setItem('short_ttl', 'value1', 50);
      await adapter.setItem('medium_ttl', 'value2', 150);
      await adapter.setItem('no_ttl', 'value3');

      // Wait for short TTL to expire
      await wait(75);

      // Cleanup should remove expired items
      const cleanedCount = await adapter.cleanup();
      expect(cleanedCount).toBeGreaterThanOrEqual(1);

      // Check that only non-expired items remain
      expect(await adapter.getItem('short_ttl')).toBeNull();
      expect(await adapter.getItem('medium_ttl')).toBe('value2');
      expect(await adapter.getItem('no_ttl')).toBe('value3');
    });

    it('should compact store file', async () => {
      // Add items with short TTL
      await adapter.setItem('compact1', 'value1', 50);
      await adapter.setItem('compact2', 'value2', 50);
      await adapter.setItem('permanent', 'permanent_value');

      // Wait for expiration
      await wait(75);

      // Compact should remove expired items
      const compactedCount = await adapter.compact();
      expect(compactedCount).toBe(2);

      // Verify writeFile was called for compaction
      expect(mockFs.promises.writeFile).toHaveBeenCalled();
    });
  });

  describe('File System Operations', () => {
    it('should handle file read errors gracefully', async () => {
      mockFs.promises.readFile.mockRejectedValue(new Error('File read error'));

      await expect(adapter.getItem('test_key')).rejects.toThrow('Failed to read store file');
    });

    it('should handle file write errors gracefully', async () => {
      mockFs.promises.writeFile.mockRejectedValue(new Error('File write error'));

      await expect(adapter.setItem('test_key', 'test_value')).rejects.toThrow('Failed to write store file');
    });

    it('should handle corrupted JSON files', async () => {
      mockFs.promises.readFile.mockResolvedValue('invalid json {');

      await expect(adapter.getItem('test_key')).rejects.toThrow('Failed to parse store file');
    });

    it('should handle file locking scenarios', async () => {
      // Simulate file being locked by another process
      let writeCount = 0;
      mockFs.promises.writeFile.mockImplementation(() => {
        writeCount++;
        if (writeCount === 1) {
          return Promise.reject(new Error('EBUSY: resource busy or locked'));
        }
        return Promise.resolve();
      });

      // Should eventually succeed after retry
      await adapter.setItem('locked_test', 'test_value');
      expect(writeCount).toBeGreaterThan(1);
    });
  });

  describe('Storage Capabilities', () => {
    it('should report correct capabilities', () => {
      assertAdapterCapabilities(adapter, 'node', true);
      
      const capabilities = adapter.getCapabilities();
      expect(capabilities.supportsBulkOperations).toBe(false);
      expect(capabilities.maxStorageSize).toBe(-1); // Unlimited
    });
  });

  describe('Health Monitoring', () => {
    it('should report healthy status when functioning', async () => {
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should report unhealthy status when disposed', async () => {
      await adapter.dispose();
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should handle health check file system errors', async () => {
      mockFs.promises.access.mockRejectedValue(new Error('File system error'));

      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid keys safely', async () => {
      const invalidKeys = ['', null, undefined, 'key/with/slashes', 'key:with:colons'];
      
      for (const key of invalidKeys) {
        try {
          await adapter.setItem(key as any, 'value');
          const result = await adapter.getItem(key as any);
          // Should either work or return null
          expect(result === null || result === 'value').toBe(true);
        } catch (error) {
          // If it throws, should be meaningful error
          expect(error).toBeInstanceOf(Error);
        }
      }
    });

    it('should handle disk space errors', async () => {
      const diskFullError = new Error('ENOSPC: no space left on device');
      mockFs.promises.writeFile.mockRejectedValue(diskFullError);

      await expect(adapter.setItem('large_item', 'x'.repeat(1000000))).rejects.toThrow('ENOSPC');
    });

    it('should handle permission errors', async () => {
      const permissionError = new Error('EACCES: permission denied');
      mockFs.promises.writeFile.mockRejectedValue(permissionError);

      await expect(adapter.setItem('permission_test', 'value')).rejects.toThrow('EACCES');
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent read/write operations', async () => {
      const operations = Array.from({ length: 10 }, async (_, i) => {
        await adapter.setItem(`concurrent_${i}`, `value_${i}`);
        return adapter.getItem(`concurrent_${i}`);
      });

      const results = await Promise.all(operations);
      
      results.forEach((result, i) => {
        expect(result).toBe(`value_${i}`);
      });
    });

    it('should handle file system race conditions', async () => {
      // Simulate race condition where file is modified between read and write
      let readCount = 0;
      mockFs.promises.readFile.mockImplementation(() => {
        readCount++;
        const data = readCount === 1 ? '{}' : '{"other_key": "other_value"}';
        return Promise.resolve(data);
      });

      await adapter.setItem('race_test', 'test_value');
      
      // Should handle the race condition gracefully
      expect(mockFs.promises.writeFile).toHaveBeenCalled();
    });
  });

  describe('Platform-Specific Features', () => {
    it('should use environment variables for configuration', async () => {
      process.env.STORAGE_DIR = '/env/storage/path';
      
      const newAdapter = new NodeStorageAdapter();
      await newAdapter.initialize({
        name: 'env_db',
        storeName: 'env_store'
      });

      expect(mockPath.join).toHaveBeenCalledWith('/env/storage/path', 'env_db');
      
      delete process.env.STORAGE_DIR;
      await newAdapter.dispose();
    });

    it('should handle different file system types', async () => {
      // Test with different path separators and file systems
      mockPath.join.mockImplementation((...args) => {
        // Simulate Windows path joining
        return args.join('\\');
      });

      const newAdapter = new NodeStorageAdapter();
      await newAdapter.initialize({
        name: 'windows_db',
        storeName: 'windows_store'
      });

      expect(mockPath.join).toHaveBeenCalled();
      await newAdapter.dispose();
    });

    it('should handle safe key generation for file systems', async () => {
      // Test keys with characters that might be problematic in file systems
      const problematicKeys = [
        'key<with>brackets',
        'key"with"quotes',
        'key|with|pipes',
        'key?with?questions',
        'key*with*asterisks'
      ];

      for (const key of problematicKeys) {
        await adapter.setItem(key, 'test_value');
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toBe('test_value');
      }
    });
  });

  describe('Memory Management', () => {
    it('should dispose resources properly', async () => {
      await adapter.dispose();
      
      // After disposal, operations should fail
      await expect(adapter.getItem('test')).rejects.toThrow('Storage adapter not initialized');
      
      // Health check should return false
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should handle multiple dispose calls', async () => {
      await adapter.dispose();
      await adapter.dispose(); // Should not throw
      
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });
  });
});
