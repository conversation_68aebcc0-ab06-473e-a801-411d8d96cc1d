/**
 * Repository Integration Tests
 * Tests storage integration with repository patterns and application usage
 */

import { initializeStorage, getRepositoryStorage } from '../../index';
import { StorageManager } from '../../StorageManager';
import { IStorageAdapter } from '../../types';
import { mockBrowserEnvironment, mockSSREnvironment, testData, wait } from '../utils/test-helpers';

// Mock localforage
jest.mock('localforage', () => {
  const mockStore = new Map();
  return {
    createInstance: jest.fn(() => ({
      ready: jest.fn().mockResolvedValue(undefined),
      getItem: jest.fn((key) => Promise.resolve(mockStore.get(key) || null)),
      setItem: jest.fn((key, value) => {
        mockStore.set(key, value);
        return Promise.resolve(value);
      }),
      removeItem: jest.fn((key) => {
        mockStore.delete(key);
        return Promise.resolve();
      }),
      clear: jest.fn(() => {
        mockStore.clear();
        return Promise.resolve();
      }),
      keys: jest.fn(() => Promise.resolve(Array.from(mockStore.keys()))),
      length: jest.fn(() => Promise.resolve(mockStore.size)),
      driver: jest.fn(() => 'asyncStorage')
    })),
    INDEXEDDB: 'asyncStorage',
    LOCALSTORAGE: 'localStorageWrapper',
    WEBSQL: 'webSQLStorage'
  };
});

// Mock repository base class
class MockRepositoryBase {
  protected storage: IStorageAdapter;
  protected storeName: string;
  protected defaultTTL?: number;

  constructor(storeName: string, defaultTTL?: number) {
    this.storeName = storeName;
    this.defaultTTL = defaultTTL;
  }

  async initialize(): Promise<void> {
    this.storage = await getRepositoryStorage(this.storeName, this.defaultTTL);
  }

  async setItem<T>(key: string, value: T, ttl?: number): Promise<void> {
    return this.storage.setItem(key, value, ttl || this.defaultTTL);
  }

  async getItem<T>(key: string): Promise<T | null> {
    return this.storage.getItem<T>(key);
  }

  async removeItem(key: string): Promise<void> {
    return this.storage.removeItem(key);
  }

  async clear(): Promise<void> {
    return this.storage.clear();
  }

  async keys(): Promise<string[]> {
    return this.storage.keys();
  }

  async cleanup(): Promise<number> {
    return this.storage.cleanup();
  }

  getCapabilities() {
    return this.storage.getCapabilities();
  }
}

// Example repository implementations
class UserRepository extends MockRepositoryBase {
  constructor() {
    super('users', 3600000); // 1 hour TTL
  }

  async saveUser(user: { id: number; name: string; email: string }): Promise<void> {
    await this.setItem(`user_${user.id}`, user);
  }

  async getUser(id: number): Promise<{ id: number; name: string; email: string } | null> {
    return this.getItem(`user_${id}`);
  }

  async saveCurrentUser(user: { id: number; name: string; email: string }): Promise<void> {
    await this.setItem('current_user', user);
  }

  async getCurrentUser(): Promise<{ id: number; name: string; email: string } | null> {
    return this.getItem('current_user');
  }

  async removeUser(id: number): Promise<void> {
    await this.removeItem(`user_${id}`);
  }
}

class SessionRepository extends MockRepositoryBase {
  constructor() {
    super('sessions', 1800000); // 30 minutes TTL
  }

  async saveSession(sessionData: { token: string; userId: number; expires: number }): Promise<void> {
    await this.setItem('current_session', sessionData);
  }

  async getSession(): Promise<{ token: string; userId: number; expires: number } | null> {
    return this.getItem('current_session');
  }

  async clearSession(): Promise<void> {
    await this.removeItem('current_session');
  }

  async isSessionValid(): Promise<boolean> {
    const session = await this.getSession();
    return session !== null && session.expires > Date.now();
  }
}

class CacheRepository extends MockRepositoryBase {
  constructor() {
    super('cache', 300000); // 5 minutes TTL
  }

  async cacheApiResponse(endpoint: string, data: any): Promise<void> {
    await this.setItem(`api_${endpoint}`, data);
  }

  async getCachedApiResponse(endpoint: string): Promise<any> {
    return this.getItem(`api_${endpoint}`);
  }

  async invalidateCache(pattern?: string): Promise<void> {
    const keys = await this.keys();
    const keysToRemove = pattern 
      ? keys.filter(key => key.includes(pattern))
      : keys;

    for (const key of keysToRemove) {
      await this.removeItem(key);
    }
  }
}

describe('Repository Integration Tests', () => {
  let cleanupMock: () => void;

  beforeEach(() => {
    cleanupMock = mockBrowserEnvironment();
  });

  afterEach(() => {
    cleanupMock();
    jest.clearAllMocks();
  });

  describe('Repository Pattern Integration', () => {
    it('should work with user repository pattern', async () => {
      await initializeStorage();
      
      const userRepo = new UserRepository();
      await userRepo.initialize();

      const testUser = { id: 1, name: 'John Doe', email: '<EMAIL>' };

      // Save user
      await userRepo.saveUser(testUser);
      
      // Retrieve user
      const retrievedUser = await userRepo.getUser(1);
      expect(retrievedUser).toEqual(testUser);

      // Save current user
      await userRepo.saveCurrentUser(testUser);
      const currentUser = await userRepo.getCurrentUser();
      expect(currentUser).toEqual(testUser);

      // Remove user
      await userRepo.removeUser(1);
      const removedUser = await userRepo.getUser(1);
      expect(removedUser).toBeNull();
    });

    it('should work with session repository pattern', async () => {
      await initializeStorage();
      
      const sessionRepo = new SessionRepository();
      await sessionRepo.initialize();

      const sessionData = {
        token: 'abc123',
        userId: 1,
        expires: Date.now() + 3600000 // 1 hour from now
      };

      // Save session
      await sessionRepo.saveSession(sessionData);
      
      // Retrieve session
      const retrievedSession = await sessionRepo.getSession();
      expect(retrievedSession).toEqual(sessionData);

      // Check session validity
      const isValid = await sessionRepo.isSessionValid();
      expect(isValid).toBe(true);

      // Clear session
      await sessionRepo.clearSession();
      const clearedSession = await sessionRepo.getSession();
      expect(clearedSession).toBeNull();
    });

    it('should work with cache repository pattern', async () => {
      await initializeStorage();
      
      const cacheRepo = new CacheRepository();
      await cacheRepo.initialize();

      const apiData = { users: [{ id: 1, name: 'John' }], total: 1 };

      // Cache API response
      await cacheRepo.cacheApiResponse('users', apiData);
      
      // Retrieve cached response
      const cachedData = await cacheRepo.getCachedApiResponse('users');
      expect(cachedData).toEqual(apiData);

      // Cache multiple responses
      await cacheRepo.cacheApiResponse('posts', { posts: [], total: 0 });
      await cacheRepo.cacheApiResponse('comments', { comments: [], total: 0 });

      // Invalidate specific cache
      await cacheRepo.invalidateCache('users');
      expect(await cacheRepo.getCachedApiResponse('users')).toBeNull();
      expect(await cacheRepo.getCachedApiResponse('posts')).toEqual({ posts: [], total: 0 });

      // Invalidate all cache
      await cacheRepo.invalidateCache();
      expect(await cacheRepo.getCachedApiResponse('posts')).toBeNull();
      expect(await cacheRepo.getCachedApiResponse('comments')).toBeNull();
    });
  });

  describe('Multi-Repository Integration', () => {
    it('should handle multiple repositories independently', async () => {
      await initializeStorage();
      
      const userRepo = new UserRepository();
      const sessionRepo = new SessionRepository();
      const cacheRepo = new CacheRepository();

      await Promise.all([
        userRepo.initialize(),
        sessionRepo.initialize(),
        cacheRepo.initialize()
      ]);

      // Each repository should use different storage
      expect(userRepo.getCapabilities().platform).toBe('browser');
      expect(sessionRepo.getCapabilities().platform).toBe('browser');
      expect(cacheRepo.getCapabilities().platform).toBe('browser');

      // Test data isolation
      const user = { id: 1, name: 'John', email: '<EMAIL>' };
      const session = { token: 'abc123', userId: 1, expires: Date.now() + 3600000 };
      const cache = { data: 'cached_data' };

      await userRepo.saveCurrentUser(user);
      await sessionRepo.saveSession(session);
      await cacheRepo.cacheApiResponse('test', cache);

      // Data should be isolated
      expect(await userRepo.getCurrentUser()).toEqual(user);
      expect(await sessionRepo.getSession()).toEqual(session);
      expect(await cacheRepo.getCachedApiResponse('test')).toEqual(cache);

      // Clearing one repository shouldn't affect others
      await userRepo.clear();
      expect(await userRepo.getCurrentUser()).toBeNull();
      expect(await sessionRepo.getSession()).toEqual(session);
      expect(await cacheRepo.getCachedApiResponse('test')).toEqual(cache);
    });

    it('should handle TTL differences across repositories', async () => {
      await initializeStorage();
      
      const userRepo = new UserRepository(); // 1 hour TTL
      const sessionRepo = new SessionRepository(); // 30 minutes TTL
      const cacheRepo = new CacheRepository(); // 5 minutes TTL

      await Promise.all([
        userRepo.initialize(),
        sessionRepo.initialize(),
        cacheRepo.initialize()
      ]);

      // Add data to all repositories
      await userRepo.saveCurrentUser({ id: 1, name: 'John', email: '<EMAIL>' });
      await sessionRepo.saveSession({ token: 'abc123', userId: 1, expires: Date.now() + 3600000 });
      await cacheRepo.cacheApiResponse('test', { data: 'test' });

      // Override TTL for testing
      await userRepo.setItem('short_ttl_user', { id: 2, name: 'Jane' }, 100);
      await sessionRepo.setItem('short_ttl_session', { token: 'def456' }, 100);
      await cacheRepo.setItem('short_ttl_cache', { data: 'short' }, 100);

      // Wait for short TTL items to expire
      await wait(150);

      // Short TTL items should be expired
      expect(await userRepo.getItem('short_ttl_user')).toBeNull();
      expect(await sessionRepo.getItem('short_ttl_session')).toBeNull();
      expect(await cacheRepo.getItem('short_ttl_cache')).toBeNull();

      // Default TTL items should still exist
      expect(await userRepo.getCurrentUser()).not.toBeNull();
      expect(await sessionRepo.getSession()).not.toBeNull();
      expect(await cacheRepo.getCachedApiResponse('test')).not.toBeNull();
    });
  });

  describe('Application Lifecycle Integration', () => {
    it('should handle application startup sequence', async () => {
      // Simulate application startup
      await initializeStorage({
        name: 'myApp',
        version: 1,
        description: 'My Application Storage'
      });

      // Initialize repositories in order
      const userRepo = new UserRepository();
      const sessionRepo = new SessionRepository();
      
      await userRepo.initialize();
      await sessionRepo.initialize();

      // Load initial data
      const existingUser = await userRepo.getCurrentUser();
      const existingSession = await sessionRepo.getSession();

      // Should handle no existing data gracefully
      expect(existingUser).toBeNull();
      expect(existingSession).toBeNull();

      // Set up initial state
      const user = { id: 1, name: 'John', email: '<EMAIL>' };
      await userRepo.saveCurrentUser(user);

      // Verify state is set
      expect(await userRepo.getCurrentUser()).toEqual(user);
    });

    it('should handle application shutdown sequence', async () => {
      await initializeStorage();
      
      const userRepo = new UserRepository();
      const sessionRepo = new SessionRepository();
      
      await userRepo.initialize();
      await sessionRepo.initialize();

      // Add some data
      await userRepo.saveCurrentUser({ id: 1, name: 'John', email: '<EMAIL>' });
      await sessionRepo.saveSession({ token: 'abc123', userId: 1, expires: Date.now() + 3600000 });

      // Simulate cleanup on shutdown
      await userRepo.cleanup();
      await sessionRepo.cleanup();

      // Data should still be accessible (cleanup only removes expired items)
      expect(await userRepo.getCurrentUser()).not.toBeNull();
      expect(await sessionRepo.getSession()).not.toBeNull();
    });

    it('should handle SSR to client hydration', async () => {
      // Start in SSR environment
      cleanupMock();
      const cleanupSSR = mockSSREnvironment();

      try {
        // During SSR
        await initializeStorage(); // Should skip initialization
        
        const ssrUserRepo = new UserRepository();
        await ssrUserRepo.initialize();
        
        // Should use memory adapter during SSR
        expect(ssrUserRepo.getCapabilities().platform).toBe('memory');
        
        // Add some data during SSR
        await ssrUserRepo.saveCurrentUser({ id: 1, name: 'SSR User', email: '<EMAIL>' });
        
        cleanupSSR();
        
        // Simulate client hydration
        cleanupMock = mockBrowserEnvironment();
        
        // After hydration, new repositories should use browser storage
        const clientUserRepo = new UserRepository();
        await clientUserRepo.initialize();
        
        expect(clientUserRepo.getCapabilities().platform).toBe('browser');
        
        // In real app, you'd transfer SSR data to client storage here
        const ssrData = await ssrUserRepo.getCurrentUser();
        if (ssrData) {
          await clientUserRepo.saveCurrentUser(ssrData);
        }
        
        expect(await clientUserRepo.getCurrentUser()).toEqual({ id: 1, name: 'SSR User', email: '<EMAIL>' });
        
      } finally {
        cleanupSSR();
      }
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle repository initialization failures', async () => {
      // Mock storage initialization to fail
      const originalGetRepositoryStorage = require('../../index').getRepositoryStorage;
      jest.spyOn(require('../../index'), 'getRepositoryStorage')
        .mockRejectedValueOnce(new Error('Storage initialization failed'));

      const userRepo = new UserRepository();
      
      await expect(userRepo.initialize()).rejects.toThrow('Storage initialization failed');

      // Restore and retry
      require('../../index').getRepositoryStorage = originalGetRepositoryStorage;
      
      await userRepo.initialize();
      expect(userRepo.getCapabilities()).toBeDefined();
    });

    it('should handle storage operation failures', async () => {
      await initializeStorage();
      
      const userRepo = new UserRepository();
      await userRepo.initialize();

      // Mock storage operation to fail
      jest.spyOn(userRepo['storage'], 'setItem').mockRejectedValue(new Error('Storage operation failed'));

      await expect(userRepo.saveCurrentUser({ id: 1, name: 'John', email: '<EMAIL>' }))
        .rejects.toThrow('Storage operation failed');

      // Other operations should still work
      const user = await userRepo.getCurrentUser();
      expect(user).toBeNull(); // No user was saved due to error
    });

    it('should handle partial repository failures', async () => {
      await initializeStorage();
      
      const userRepo = new UserRepository();
      const sessionRepo = new SessionRepository();
      
      await userRepo.initialize();
      await sessionRepo.initialize();

      // Mock one repository to fail
      jest.spyOn(userRepo['storage'], 'setItem').mockRejectedValue(new Error('User storage failed'));

      // User repository should fail
      await expect(userRepo.saveCurrentUser({ id: 1, name: 'John', email: '<EMAIL>' }))
        .rejects.toThrow('User storage failed');

      // Session repository should still work
      await sessionRepo.saveSession({ token: 'abc123', userId: 1, expires: Date.now() + 3600000 });
      expect(await sessionRepo.getSession()).not.toBeNull();
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large datasets in repositories', async () => {
      await initializeStorage();
      
      const cacheRepo = new CacheRepository();
      await cacheRepo.initialize();

      // Cache large dataset
      const largeDataset = testData.large; // 1000 items
      
      const startTime = Date.now();
      await cacheRepo.cacheApiResponse('large_dataset', largeDataset);
      const cached = await cacheRepo.getCachedApiResponse('large_dataset');
      const endTime = Date.now();

      expect(cached).toEqual(largeDataset);
      expect(cached).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(2000); // Should complete within 2 seconds
    });

    it('should handle concurrent repository operations', async () => {
      await initializeStorage();
      
      const userRepo = new UserRepository();
      await userRepo.initialize();

      // Perform concurrent user operations
      const operations = Array.from({ length: 10 }, async (_, i) => {
        const user = { id: i, name: `User ${i}`, email: `user${i}@example.com` };
        await userRepo.saveUser(user);
        return userRepo.getUser(i);
      });

      const results = await Promise.all(operations);
      
      results.forEach((user, i) => {
        expect(user).toEqual({ id: i, name: `User ${i}`, email: `user${i}@example.com` });
      });
    });

    it('should handle repository cleanup efficiently', async () => {
      await initializeStorage();
      
      const cacheRepo = new CacheRepository();
      await cacheRepo.initialize();

      // Add many items with short TTL
      const itemCount = 100;
      for (let i = 0; i < itemCount; i++) {
        await cacheRepo.setItem(`item_${i}`, `value_${i}`, 100);
      }

      // Wait for expiration
      await wait(150);

      // Cleanup should be efficient
      const startTime = Date.now();
      const cleanedCount = await cacheRepo.cleanup();
      const endTime = Date.now();

      expect(cleanedCount).toBe(itemCount);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });
  });
});
