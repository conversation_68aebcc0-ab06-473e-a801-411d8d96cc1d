/**
 * Comprehensive tests for ReactNativeStorageAdapter
 * Tests React Native-specific storage functionality including AsyncStorage integration
 */

import { ReactNativeStorageAdapter } from '../../../adapters/ReactNativeStorageAdapter';
import { mockReactNativeEnvironment, testBasicOperations, testTTLFunctionality, assertAdapterCapabilities, testData, wait } from '../../utils/test-helpers';

describe('ReactNativeStorageAdapter', () => {
  let adapter: ReactNativeStorageAdapter;
  let cleanupRNMock: () => void;
  let mockAsyncStorage: any;

  beforeEach(async () => {
    cleanupRNMock = mockReactNativeEnvironment();
    mockAsyncStorage = (global as any).AsyncStorage;
    
    // Setup mock AsyncStorage behavior
    const mockStore = new Map();
    
    mockAsyncStorage.getItem.mockImplementation((key: string) => 
      Promise.resolve(mockStore.get(key) || null)
    );
    
    mockAsyncStorage.setItem.mockImplementation((key: string, value: string) => {
      mockStore.set(key, value);
      return Promise.resolve();
    });
    
    mockAsyncStorage.removeItem.mockImplementation((key: string) => {
      mockStore.delete(key);
      return Promise.resolve();
    });
    
    mockAsyncStorage.clear.mockImplementation(() => {
      mockStore.clear();
      return Promise.resolve();
    });
    
    mockAsyncStorage.getAllKeys.mockImplementation(() => 
      Promise.resolve(Array.from(mockStore.keys()))
    );
    
    mockAsyncStorage.multiGet.mockImplementation((keys: string[]) => 
      Promise.resolve(keys.map(key => [key, mockStore.get(key) || null]))
    );
    
    mockAsyncStorage.multiSet.mockImplementation((keyValuePairs: [string, string][]) => {
      keyValuePairs.forEach(([key, value]) => mockStore.set(key, value));
      return Promise.resolve();
    });
    
    mockAsyncStorage.multiRemove.mockImplementation((keys: string[]) => {
      keys.forEach(key => mockStore.delete(key));
      return Promise.resolve();
    });

    adapter = new ReactNativeStorageAdapter();
    await adapter.initialize({
      name: 'test_rn_db',
      storeName: 'test_rn_store'
    });
  });

  afterEach(async () => {
    if (adapter) {
      await adapter.dispose();
    }
    cleanupRNMock();
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with default configuration', async () => {
      const newAdapter = new ReactNativeStorageAdapter();
      await newAdapter.initialize({
        name: 'default_rn_db',
        storeName: 'default_rn_store'
      });

      expect(newAdapter).toBeDefined();
      const isHealthy = await newAdapter.isHealthy();
      expect(isHealthy).toBe(true);

      await newAdapter.dispose();
    });

    it('should initialize with custom key prefix', async () => {
      const newAdapter = new ReactNativeStorageAdapter();
      await newAdapter.initialize({
        name: 'custom_db',
        storeName: 'custom_store',
        platformOptions: {
          keyPrefix: 'custom_prefix_'
        }
      });

      await newAdapter.setItem('test_key', 'test_value');
      
      // Verify the key was stored with custom prefix
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        expect.stringContaining('custom_prefix_'),
        expect.any(String)
      );

      await newAdapter.dispose();
    });

    it('should handle AsyncStorage not available', async () => {
      // Remove AsyncStorage to simulate unavailable environment
      delete (global as any).AsyncStorage;

      const newAdapter = new ReactNativeStorageAdapter();
      await expect(newAdapter.initialize({
        name: 'failing_db',
        storeName: 'failing_store'
      })).rejects.toThrow('AsyncStorage not found');
    });

    it('should handle AsyncStorage initialization failure', async () => {
      mockAsyncStorage.getAllKeys.mockRejectedValue(new Error('AsyncStorage initialization failed'));

      const newAdapter = new ReactNativeStorageAdapter();
      await expect(newAdapter.initialize({
        name: 'failing_db',
        storeName: 'failing_store'
      })).rejects.toThrow('Failed to initialize React Native storage');
    });
  });

  describe('Basic Operations', () => {
    it('should perform all basic storage operations', async () => {
      await testBasicOperations(adapter);
    });

    it('should handle different data types correctly', async () => {
      // Test simple data types
      for (const [key, value] of Object.entries(testData.simple)) {
        await adapter.setItem(key, value);
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toEqual(value);
      }

      // Test complex data types
      for (const [key, value] of Object.entries(testData.complex)) {
        await adapter.setItem(key, value);
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toEqual(value);
      }
    });

    it('should use proper key prefixing', async () => {
      await adapter.setItem('prefix_test', 'test_value');
      
      // Verify AsyncStorage was called with prefixed key
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        expect.stringMatching(/^test_rn_db:test_rn_store:/),
        expect.any(String)
      );
    });

    it('should handle large datasets', async () => {
      await adapter.setItem('large_dataset', testData.large);
      const retrieved = await adapter.getItem('large_dataset');
      expect(retrieved).toEqual(testData.large);
    });
  });

  describe('TTL Functionality', () => {
    it('should handle TTL correctly', async () => {
      await testTTLFunctionality(adapter);
    });

    it('should cleanup expired items', async () => {
      // Add items with different TTLs
      await adapter.setItem('short_ttl', 'value1', 50);
      await adapter.setItem('medium_ttl', 'value2', 150);
      await adapter.setItem('no_ttl', 'value3');

      // Wait for short TTL to expire
      await wait(75);

      // Cleanup should remove expired items
      const cleanedCount = await adapter.cleanup();
      expect(cleanedCount).toBeGreaterThanOrEqual(1);

      // Check that only non-expired items remain
      expect(await adapter.getItem('short_ttl')).toBeNull();
      expect(await adapter.getItem('medium_ttl')).toBe('value2');
      expect(await adapter.getItem('no_ttl')).toBe('value3');
    });
  });

  describe('Bulk Operations', () => {
    it('should support bulk operations efficiently', async () => {
      const capabilities = adapter.getCapabilities();
      expect(capabilities.supportsBulkOperations).toBe(true);

      // Test bulk set
      const items = {
        'bulk1': 'value1',
        'bulk2': 'value2',
        'bulk3': 'value3'
      };

      for (const [key, value] of Object.entries(items)) {
        await adapter.setItem(key, value);
      }

      // Verify multiSet was used for efficiency
      expect(mockAsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should handle bulk operation failures gracefully', async () => {
      mockAsyncStorage.multiSet.mockRejectedValue(new Error('Bulk operation failed'));

      // Should fallback to individual operations
      await adapter.setItem('fallback_test', 'test_value');
      expect(mockAsyncStorage.setItem).toHaveBeenCalled();
    });
  });

  describe('Storage Capabilities', () => {
    it('should report correct capabilities', () => {
      assertAdapterCapabilities(adapter, 'react-native', true);
      
      const capabilities = adapter.getCapabilities();
      expect(capabilities.supportsBulkOperations).toBe(true);
      expect(capabilities.maxStorageSize).toBe(6 * 1024 * 1024); // 6MB
    });

    it('should provide storage usage information', async () => {
      const usage = await adapter.getStorageUsage();
      
      expect(usage).toHaveProperty('used');
      expect(usage).toHaveProperty('available');
      expect(usage).toHaveProperty('total');
      expect(typeof usage.used).toBe('number');
      expect(typeof usage.available).toBe('number');
      expect(typeof usage.total).toBe('number');
    });
  });

  describe('Health Monitoring', () => {
    it('should report healthy status when functioning', async () => {
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should report unhealthy status when disposed', async () => {
      await adapter.dispose();
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should handle health check AsyncStorage errors', async () => {
      mockAsyncStorage.getAllKeys.mockRejectedValue(new Error('AsyncStorage error'));

      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle AsyncStorage errors gracefully', async () => {
      mockAsyncStorage.setItem.mockRejectedValue(new Error('Storage full'));
      
      await expect(adapter.setItem('test', 'value')).rejects.toThrow('Storage full');
    });

    it('should handle invalid keys', async () => {
      const invalidKeys = ['', null, undefined];
      
      for (const key of invalidKeys) {
        try {
          await adapter.setItem(key as any, 'value');
          const result = await adapter.getItem(key as any);
          // Should either work or return null
          expect(result === null || result === 'value').toBe(true);
        } catch (error) {
          // If it throws, should be meaningful error
          expect(error).toBeInstanceOf(Error);
        }
      }
    });

    it('should handle quota exceeded scenarios', async () => {
      const quotaError = new Error('Quota exceeded');
      mockAsyncStorage.setItem.mockRejectedValue(quotaError);
      
      await expect(adapter.setItem('large_item', 'x'.repeat(1000000))).rejects.toThrow('Quota exceeded');
    });

    it('should handle JSON serialization errors', async () => {
      const circularObject: any = {};
      circularObject.self = circularObject;
      
      await expect(adapter.setItem('circular', circularObject)).rejects.toThrow();
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent read/write operations', async () => {
      const operations = Array.from({ length: 10 }, async (_, i) => {
        await adapter.setItem(`concurrent_${i}`, `value_${i}`);
        return adapter.getItem(`concurrent_${i}`);
      });

      const results = await Promise.all(operations);
      
      results.forEach((result, i) => {
        expect(result).toBe(`value_${i}`);
      });
    });

    it('should handle concurrent cleanup operations', async () => {
      // Add items with short TTL
      const setOperations = Array.from({ length: 5 }, (_, i) =>
        adapter.setItem(`cleanup_${i}`, `value_${i}`, 50)
      );
      await Promise.all(setOperations);

      // Wait for expiration
      await wait(75);

      // Run multiple cleanup operations concurrently
      const cleanupOperations = Array.from({ length: 3 }, () => adapter.cleanup());
      const results = await Promise.all(cleanupOperations);

      // At least one cleanup should have removed items
      const totalCleaned = results.reduce((sum, count) => sum + count, 0);
      expect(totalCleaned).toBeGreaterThanOrEqual(5);
    });
  });

  describe('React Native Specific Features', () => {
    it('should handle app state changes', async () => {
      // Test that storage continues to work during app state changes
      await adapter.setItem('app_state_test', 'test_value');
      
      // Simulate app going to background/foreground
      const value = await adapter.getItem('app_state_test');
      expect(value).toBe('test_value');
    });

    it('should handle device storage limitations', async () => {
      // Test behavior when device storage is low
      const capabilities = adapter.getCapabilities();
      expect(capabilities.maxStorageSize).toBe(6 * 1024 * 1024);
      
      // Verify storage usage tracking
      const usage = await adapter.getStorageUsage();
      expect(usage.total).toBeGreaterThanOrEqual(0);
    });

    it('should work with different React Native versions', async () => {
      // Test compatibility with different AsyncStorage implementations
      expect(mockAsyncStorage.getItem).toBeDefined();
      expect(mockAsyncStorage.setItem).toBeDefined();
      expect(mockAsyncStorage.multiGet).toBeDefined();
      expect(mockAsyncStorage.multiSet).toBeDefined();
    });

    it('should handle safe key generation for AsyncStorage', async () => {
      // Test keys that might be problematic in AsyncStorage
      const problematicKeys = [
        'key with spaces',
        'key.with.dots',
        'key-with-dashes',
        'key_with_underscores',
        'key/with/slashes'
      ];

      for (const key of problematicKeys) {
        await adapter.setItem(key, 'test_value');
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toBe('test_value');
      }
    });
  });

  describe('Memory Management', () => {
    it('should dispose resources properly', async () => {
      await adapter.dispose();
      
      // After disposal, operations should fail
      await expect(adapter.getItem('test')).rejects.toThrow('Storage adapter not initialized');
      
      // Health check should return false
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should handle multiple dispose calls', async () => {
      await adapter.dispose();
      await adapter.dispose(); // Should not throw
      
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should clean up AsyncStorage reference on dispose', async () => {
      await adapter.dispose();
      
      // AsyncStorage reference should be cleaned up
      expect(adapter['AsyncStorage']).toBeNull();
    });
  });
});
