/**
 * Server-Side Rendering (SSR) Environment Tests
 * Tests storage functionality during SSR scenarios including detection, fallbacks, and hydration
 */

import { StorageAdapterFactory } from '../../StorageFactory';
import { StorageManager } from '../../StorageManager';
import { detectPlatform, isSSR, isBrowser, isNode } from '../../platformDetection';
import { initializeStorage, getRepositoryStorage } from '../../index';
import { mockSSREnvironment } from '../utils/test-helpers';

describe('SSR Environment Tests', () => {
  let cleanupSSRMock: () => void;

  beforeEach(() => {
    cleanupSSRMock = mockSSREnvironment();
  });

  afterEach(() => {
    cleanupSSRMock();
    jest.clearAllMocks();
    jest.resetModules();
  });

  describe('SSR Detection', () => {
    it('should correctly detect SSR environment', () => {
      const isSSREnv = isSSR();
      expect(isSSREnv).toBe(true);

      const isBrowserEnv = isBrowser();
      expect(isBrowserEnv).toBe(false);

      const platform = detectPlatform();
      expect(platform.isServer).toBe(true);
      expect(platform.isBrowser).toBe(false);
    });

    it('should detect platform as Node.js during SSR', () => {
      // In SSR, we're still running in Node.js but without window
      const isNodeEnv = isNode();
      expect(isNodeEnv).toBe(true);

      const platform = detectPlatform();
      expect(platform.platform).toBe('node');
      expect(platform.isServer).toBe(true);
    });

    it('should provide SSR-specific platform details', () => {
      const platform = detectPlatform();
      expect(platform.details).toHaveProperty('isSSR');
      expect(platform.details.isSSR).toBe(true);
    });
  });

  describe('Storage Initialization During SSR', () => {
    it('should skip storage initialization during SSR', async () => {
      // initializeStorage should return early during SSR
      await expect(initializeStorage()).resolves.toBeUndefined();
      
      // No storage manager should be created
      // This tests the early return in initializeStorage when window is undefined
    });

    it('should handle storage initialization with custom config during SSR', async () => {
      const customConfig = {
        name: 'ssr_test_db',
        version: 1,
        description: 'SSR test database'
      };

      await expect(initializeStorage(customConfig)).resolves.toBeUndefined();
    });

    it('should not throw errors during SSR initialization', async () => {
      // Multiple initialization calls should be safe during SSR
      await initializeStorage();
      await initializeStorage();
      await initializeStorage();
      
      // Should not throw any errors
    });
  });

  describe('Repository Storage During SSR', () => {
    it('should return memory adapter for repository storage during SSR', async () => {
      const storage = await getRepositoryStorage('ssr_test_repo');
      
      expect(storage).toBeDefined();
      expect(storage.getCapabilities().platform).toBe('memory');
      expect(storage.getCapabilities().persistent).toBe(false);
    });

    it('should handle TTL in SSR memory storage', async () => {
      const storage = await getRepositoryStorage('ssr_ttl_repo', 1000);
      
      await storage.setItem('ssr_key', 'ssr_value');
      const value = await storage.getItem('ssr_key');
      expect(value).toBe('ssr_value');
    });

    it('should create isolated storage instances for different repositories during SSR', async () => {
      const storage1 = await getRepositoryStorage('ssr_repo1');
      const storage2 = await getRepositoryStorage('ssr_repo2');
      
      expect(storage1).toBeDefined();
      expect(storage2).toBeDefined();
      
      // Should be different instances
      expect(storage1).not.toBe(storage2);
      
      // Test isolation
      await storage1.setItem('test_key', 'value1');
      await storage2.setItem('test_key', 'value2');
      
      expect(await storage1.getItem('test_key')).toBe('value1');
      expect(await storage2.getItem('test_key')).toBe('value2');
    });
  });

  describe('Storage Factory During SSR', () => {
    it('should recommend memory adapter during SSR', () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapterType = factory.getAdapterType();
      
      // During SSR, should default to memory or node adapter
      expect(['memory', 'node'].includes(adapterType)).toBe(true);
    });

    it('should create memory adapter when browser adapter is requested during SSR', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      // Browser adapter should not be available during SSR
      const isBrowserAvailable = factory.isAdapterAvailable('browser');
      expect(isBrowserAvailable).toBe(false);
      
      // Should fallback to available adapter
      const adapter = await factory.createAdapterWithFallback({
        name: 'ssr_fallback_db',
        storeName: 'ssr_fallback_store'
      }, ['browser', 'memory']);
      
      expect(adapter).toBeDefined();
      expect(adapter.getCapabilities().platform).toBe('memory');
      
      await adapter.dispose();
    });

    it('should handle adapter creation failures gracefully during SSR', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      // Try to create an adapter that's not available
      await expect(factory.createSpecificAdapter('browser', {
        name: 'ssr_fail_db',
        storeName: 'ssr_fail_store'
      })).rejects.toThrow('not available in this environment');
    });
  });

  describe('Storage Manager During SSR', () => {
    it('should create storage manager during SSR', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'ssr_manager_db',
        version: 1
      });
      
      expect(manager).toBeDefined();
      await manager.cleanup();
    });

    it('should provide memory storage instances during SSR', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'ssr_storage_db',
        version: 1
      });
      
      const storage = await manager.getStorage('ssr_store');
      expect(storage).toBeDefined();
      expect(storage.getCapabilities().platform).toBe('memory');
      
      await manager.cleanup();
    });

    it('should handle health monitoring during SSR', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'ssr_health_db',
        version: 1
      });
      
      await manager.getStorage('store1');
      await manager.getStorage('store2');
      
      const healthStatus = await manager.getHealthStatus();
      expect(healthStatus.healthy).toBe(true);
      expect(healthStatus.adapters).toHaveLength(2);
      
      await manager.cleanup();
    });
  });

  describe('SSR to Client Hydration Simulation', () => {
    it('should handle transition from SSR to client environment', async () => {
      // Start in SSR environment
      const ssrStorage = await getRepositoryStorage('hydration_test');
      expect(ssrStorage.getCapabilities().platform).toBe('memory');
      
      // Add some data during SSR
      await ssrStorage.setItem('ssr_data', 'ssr_value');
      
      // Simulate hydration by restoring window object
      cleanupSSRMock();
      
      // Mock browser environment for hydration
      (global as any).window = {
        localStorage: {
          getItem: jest.fn(),
          setItem: jest.fn(),
          removeItem: jest.fn(),
          clear: jest.fn()
        },
        indexedDB: {
          open: jest.fn()
        }
      };
      
      // After hydration, new storage instances should use browser adapter
      const clientStorage = await getRepositoryStorage('hydration_test');
      
      // Note: In real scenarios, you'd need to handle data migration
      // This test just verifies the adapter type changes
      expect(clientStorage.getCapabilities().platform).toBe('browser');
      
      // Cleanup
      delete (global as any).window;
    });

    it('should handle data persistence across SSR/client boundary', async () => {
      // This test simulates how data might be transferred from SSR to client
      const ssrData = {
        'user_preferences': { theme: 'dark', language: 'en' },
        'session_data': { token: 'abc123', expires: Date.now() + 3600000 }
      };
      
      // During SSR, data would be stored in memory
      const ssrStorage = await getRepositoryStorage('persistence_test');
      for (const [key, value] of Object.entries(ssrData)) {
        await ssrStorage.setItem(key, value);
      }
      
      // Verify data is available during SSR
      for (const [key, expectedValue] of Object.entries(ssrData)) {
        const value = await ssrStorage.getItem(key);
        expect(value).toEqual(expectedValue);
      }
      
      // In real applications, this data would be serialized and sent to client
      const serializedData = JSON.stringify(ssrData);
      expect(serializedData).toBeDefined();
      
      // Client would then deserialize and store in persistent storage
      const deserializedData = JSON.parse(serializedData);
      expect(deserializedData).toEqual(ssrData);
    });
  });

  describe('SSR Performance and Memory Management', () => {
    it('should handle multiple SSR requests efficiently', async () => {
      // Simulate multiple concurrent SSR requests
      const requests = Array.from({ length: 10 }, async (_, i) => {
        const storage = await getRepositoryStorage(`ssr_request_${i}`);
        await storage.setItem('request_data', `data_${i}`);
        return storage.getItem('request_data');
      });
      
      const results = await Promise.all(requests);
      
      results.forEach((result, i) => {
        expect(result).toBe(`data_${i}`);
      });
    });

    it('should clean up memory storage after SSR request', async () => {
      const storage = await getRepositoryStorage('cleanup_test');
      
      // Add some data
      await storage.setItem('temp_data', 'temp_value');
      expect(await storage.getItem('temp_data')).toBe('temp_value');
      
      // Dispose storage (simulating end of SSR request)
      await storage.dispose();
      
      // Storage should be cleaned up
      const isHealthy = await storage.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should handle memory limits during SSR', async () => {
      const storage = await getRepositoryStorage('memory_limit_test');
      
      // Memory adapter should have reasonable limits
      const capabilities = storage.getCapabilities();
      expect(capabilities.maxStorageSize).toBeGreaterThan(0);
      
      // Should handle large data sets within limits
      const largeData = Array.from({ length: 1000 }, (_, i) => ({ id: i, data: `item_${i}` }));
      await storage.setItem('large_ssr_data', largeData);
      
      const retrieved = await storage.getItem('large_ssr_data');
      expect(retrieved).toHaveLength(1000);
    });
  });

  describe('SSR Error Handling', () => {
    it('should handle storage errors gracefully during SSR', async () => {
      const storage = await getRepositoryStorage('error_test');
      
      // Even if storage operations fail, SSR should continue
      try {
        await storage.setItem('error_key', 'error_value');
        const value = await storage.getItem('error_key');
        expect(value).toBe('error_value');
      } catch (error) {
        // If errors occur, they should be handled gracefully
        expect(error).toBeInstanceOf(Error);
      }
    });

    it('should handle invalid data during SSR', async () => {
      const storage = await getRepositoryStorage('invalid_data_test');
      
      // Test with various invalid data types
      const invalidData = [
        undefined,
        null,
        function() { return 'function'; },
        Symbol('symbol'),
        new Date(), // Should be serializable
        /regex/
      ];
      
      for (let i = 0; i < invalidData.length; i++) {
        try {
          await storage.setItem(`invalid_${i}`, invalidData[i]);
          const retrieved = await storage.getItem(`invalid_${i}`);
          // Some data types might be serialized differently
          expect(retrieved !== undefined).toBe(true);
        } catch (error) {
          // Some data types might not be serializable
          expect(error).toBeInstanceOf(Error);
        }
      }
    });

    it('should handle concurrent SSR operations safely', async () => {
      const storage = await getRepositoryStorage('concurrent_ssr_test');
      
      // Simulate concurrent operations during SSR
      const operations = Array.from({ length: 20 }, async (_, i) => {
        await storage.setItem(`concurrent_${i}`, `value_${i}`);
        return storage.getItem(`concurrent_${i}`);
      });
      
      const results = await Promise.all(operations);
      
      // All operations should complete successfully
      results.forEach((result, i) => {
        expect(result).toBe(`value_${i}`);
      });
    });
  });

  describe('SSR Configuration and Options', () => {
    it('should respect SSR-specific configuration', async () => {
      const storage = await getRepositoryStorage('config_test', 5000); // 5 second TTL
      
      await storage.setItem('ttl_test', 'ttl_value');
      const value = await storage.getItem('ttl_test');
      expect(value).toBe('ttl_value');
      
      // TTL should work even in SSR memory storage
      const capabilities = storage.getCapabilities();
      expect(capabilities.supportsTTL).toBe(true);
    });

    it('should handle different SSR scenarios', async () => {
      // Test different repository names and configurations
      const scenarios = [
        { name: 'user_data', ttl: 3600000 },
        { name: 'session_cache', ttl: 1800000 },
        { name: 'temp_storage', ttl: 300000 },
        { name: 'permanent_data' } // No TTL
      ];
      
      for (const scenario of scenarios) {
        const storage = await getRepositoryStorage(scenario.name, scenario.ttl);
        
        expect(storage).toBeDefined();
        expect(storage.getCapabilities().platform).toBe('memory');
        
        await storage.setItem('test_key', 'test_value');
        const value = await storage.getItem('test_key');
        expect(value).toBe('test_value');
      }
    });
  });

  describe('SSR Integration with Next.js Patterns', () => {
    it('should work with getServerSideProps pattern', async () => {
      // Simulate Next.js getServerSideProps
      const getServerSideProps = async () => {
        const storage = await getRepositoryStorage('server_props');
        
        // Fetch data during SSR
        await storage.setItem('server_data', { message: 'Hello from server' });
        const data = await storage.getItem('server_data');
        
        return {
          props: {
            serverData: data
          }
        };
      };
      
      const result = await getServerSideProps();
      expect(result.props.serverData).toEqual({ message: 'Hello from server' });
    });

    it('should work with getStaticProps pattern', async () => {
      // Simulate Next.js getStaticProps
      const getStaticProps = async () => {
        const storage = await getRepositoryStorage('static_props');
        
        // Cache data during static generation
        await storage.setItem('static_data', { generated: Date.now() });
        const data = await storage.getItem('static_data');
        
        return {
          props: {
            staticData: data
          }
        };
      };
      
      const result = await getStaticProps();
      expect(result.props.staticData).toHaveProperty('generated');
    });

    it('should handle API routes during SSR', async () => {
      // Simulate Next.js API route
      const apiHandler = async () => {
        const storage = await getRepositoryStorage('api_cache');
        
        // Check cache first
        let data = await storage.getItem('api_data');
        
        if (!data) {
          // Simulate API call
          data = { result: 'API response', timestamp: Date.now() };
          await storage.setItem('api_data', data, 300000); // 5 minute cache
        }
        
        return data;
      };
      
      const result = await apiHandler();
      expect(result).toHaveProperty('result');
      expect(result).toHaveProperty('timestamp');
    });
  });
});
