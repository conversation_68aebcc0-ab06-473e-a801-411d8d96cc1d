/**
 * Test utilities and helpers for storage module testing
 */

import { IStorageAdapter, StorageConfig } from '../../types';
import { MemoryStorageAdapter } from '../../adapters/MemoryStorageAdapter';

/**
 * Create a test storage adapter with default configuration
 */
export async function createTestStorage(
  storeName: string = 'test_store',
  config?: Partial<StorageConfig>
): Promise<IStorageAdapter> {
  const adapter = new MemoryStorageAdapter();
  await adapter.initialize({
    name: 'test_db',
    storeName,
    ...config
  });
  return adapter;
}

/**
 * Mock browser environment globals
 */
export function mockBrowserEnvironment() {
  const originalWindow = global.window;
  const originalDocument = global.document;
  const originalNavigator = global.navigator;
  const originalLocalStorage = global.localStorage;
  const originalIndexedDB = global.indexedDB;

  // Mock window object
  (global as any).window = {
    localStorage: {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
      length: 0,
      key: jest.fn()
    },
    indexedDB: {
      open: jest.fn(),
      deleteDatabase: jest.fn(),
      cmp: jest.fn()
    },
    navigator: {
      userAgent: 'Mozilla/5.0 (Test Browser)',
      platform: 'Test'
    }
  };

  // Mock document
  (global as any).document = {
    createElement: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  };

  // Mock navigator
  (global as any).navigator = {
    userAgent: 'Mozilla/5.0 (Test Browser)',
    platform: 'Test'
  };

  // Mock localStorage
  (global as any).localStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
    length: 0,
    key: jest.fn()
  };

  // Mock IndexedDB
  (global as any).indexedDB = {
    open: jest.fn(),
    deleteDatabase: jest.fn(),
    cmp: jest.fn()
  };

  return () => {
    global.window = originalWindow;
    global.document = originalDocument;
    global.navigator = originalNavigator;
    global.localStorage = originalLocalStorage;
    global.indexedDB = originalIndexedDB;
  };
}

/**
 * Mock Node.js environment
 */
export function mockNodeEnvironment() {
  const originalProcess = global.process;
  const originalRequire = global.require;

  // Mock process object
  (global as any).process = {
    versions: { node: '18.0.0' },
    env: { NODE_ENV: 'test' },
    cwd: jest.fn(() => '/test/directory'),
    platform: 'linux'
  };

  // Mock require function
  (global as any).require = jest.fn();

  return () => {
    global.process = originalProcess;
    global.require = originalRequire;
  };
}

/**
 * Mock React Native environment
 */
export function mockReactNativeEnvironment() {
  const originalNavigator = global.navigator;
  const originalHermesInternal = (global as any).HermesInternal;
  const originalAsyncStorage = (global as any).AsyncStorage;

  // Mock React Native navigator
  (global as any).navigator = {
    product: 'ReactNative',
    userAgent: 'ReactNative/0.72.0'
  };

  // Mock Hermes engine
  (global as any).HermesInternal = {
    getRuntimeProperties: () => ({
      'OSS Release Version': '0.12.0'
    })
  };

  // Mock AsyncStorage
  (global as any).AsyncStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
    getAllKeys: jest.fn(),
    multiGet: jest.fn(),
    multiSet: jest.fn(),
    multiRemove: jest.fn()
  };

  return () => {
    global.navigator = originalNavigator;
    (global as any).HermesInternal = originalHermesInternal;
    (global as any).AsyncStorage = originalAsyncStorage;
  };
}

/**
 * Mock SSR environment (no window object)
 */
export function mockSSREnvironment() {
  const originalWindow = global.window;
  const originalDocument = global.document;

  // Remove window and document to simulate SSR
  delete (global as any).window;
  delete (global as any).document;

  return () => {
    global.window = originalWindow;
    global.document = originalDocument;
  };
}

/**
 * Create mock file system for Node.js testing
 */
export function createMockFileSystem() {
  const mockFs = {
    promises: {
      mkdir: jest.fn().mockResolvedValue(undefined),
      access: jest.fn().mockResolvedValue(undefined),
      readFile: jest.fn().mockResolvedValue('{}'),
      writeFile: jest.fn().mockResolvedValue(undefined),
      unlink: jest.fn().mockResolvedValue(undefined),
      readdir: jest.fn().mockResolvedValue([])
    }
  };

  const mockPath = {
    join: jest.fn((...args) => args.join('/')),
    dirname: jest.fn((path) => path.split('/').slice(0, -1).join('/')),
    basename: jest.fn((path) => path.split('/').pop())
  };

  return { mockFs, mockPath };
}

/**
 * Wait for a specified amount of time
 */
export function wait(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Generate test data of various types
 */
export const testData = {
  simple: {
    string: 'test string',
    number: 42,
    boolean: true,
    null: null
  },
  complex: {
    array: [1, 2, 3, 'four', { five: 5 }],
    object: {
      nested: {
        deeply: {
          value: 'deep value'
        }
      }
    },
    date: new Date().toISOString(),
    mixed: {
      string: 'test',
      number: 123,
      boolean: false,
      array: ['a', 'b', 'c'],
      object: { key: 'value' }
    }
  },
  large: Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    data: `Item ${i}`,
    timestamp: Date.now() + i,
    metadata: {
      type: 'test',
      category: i % 10,
      tags: [`tag${i % 5}`, `category${i % 3}`]
    }
  }))
};

/**
 * Assert that an adapter has expected capabilities
 */
export function assertAdapterCapabilities(
  adapter: IStorageAdapter,
  expectedPlatform: string,
  expectedPersistent: boolean
) {
  const capabilities = adapter.getCapabilities();
  expect(capabilities.platform).toBe(expectedPlatform);
  expect(capabilities.persistent).toBe(expectedPersistent);
  expect(capabilities.supportsTTL).toBe(true);
  expect(typeof capabilities.maxStorageSize).toBe('number');
}

/**
 * Test basic storage operations
 */
export async function testBasicOperations(adapter: IStorageAdapter) {
  // Test setItem and getItem
  await adapter.setItem('test_key', 'test_value');
  const value = await adapter.getItem('test_key');
  expect(value).toBe('test_value');

  // Test complex data
  const complexData = { nested: { value: 42 }, array: [1, 2, 3] };
  await adapter.setItem('complex_key', complexData);
  const retrievedComplex = await adapter.getItem('complex_key');
  expect(retrievedComplex).toEqual(complexData);

  // Test removeItem
  await adapter.removeItem('test_key');
  const removedValue = await adapter.getItem('test_key');
  expect(removedValue).toBeNull();

  // Test keys and length
  await adapter.setItem('key1', 'value1');
  await adapter.setItem('key2', 'value2');
  
  const keys = await adapter.keys();
  expect(keys).toContain('key1');
  expect(keys).toContain('key2');
  expect(keys).toContain('complex_key');

  const length = await adapter.length();
  expect(length).toBe(3);

  // Test clear
  await adapter.clear();
  const finalLength = await adapter.length();
  expect(finalLength).toBe(0);
}

/**
 * Test TTL functionality
 */
export async function testTTLFunctionality(adapter: IStorageAdapter) {
  const shortTTL = 100; // 100ms
  
  // Set item with TTL
  await adapter.setItem('ttl_key', 'ttl_value', shortTTL);
  
  // Should be available immediately
  let value = await adapter.getItem('ttl_key');
  expect(value).toBe('ttl_value');
  
  // Wait for expiration
  await wait(150);
  
  // Should be expired now
  value = await adapter.getItem('ttl_key');
  expect(value).toBeNull();
}
