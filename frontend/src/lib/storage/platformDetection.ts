/**
 * Platform detection utilities for determining the runtime environment.
 * 
 * This module provides functions to detect whether the code is running in
 * a browser, Node.js, React Native, or other environments, enabling the
 * selection of appropriate storage adapters.
 */

import type { PlatformInfo } from './types';

/**
 * Detect the current platform and environment
 */
export function detectPlatform(): PlatformInfo {
    const info: PlatformInfo = {
        platform: 'unknown',
        isServer: false,
        isBrowser: false,
        isReactNative: false,
        details: {}
    };

    // Check for React Native first (it has some browser-like globals)
    if (isReactNative()) {
        info.platform = 'react-native';
        info.isReactNative = true;
        info.details.reactNativeVersion = getReactNativeVersion();
        return info;
    }

    // Check for browser environment
    if (isBrowser()) {
        info.platform = 'browser';
        info.isBrowser = true;
        info.details.userAgent = getUserAgent();
        return info;
    }

    // Check for Node.js environment
    if (isNode()) {
        info.platform = 'node';
        info.isServer = true;
        info.details.nodeVersion = getNodeVersion();
        info.details.isSSR = isSSR();
        return info;
    }

    return info;
}

/**
 * Check if running in a browser environment
 */
export function isBrowser(): boolean {
    return typeof window !== 'undefined' && 
           typeof window.document !== 'undefined' &&
           typeof window.localStorage !== 'undefined';
}

/**
 * Check if running in Node.js environment
 */
export function isNode(): boolean {
    return typeof process !== 'undefined' && 
           process.versions != null && 
           process.versions.node != null &&
           typeof window === 'undefined';
}

/**
 * Check if running in React Native environment
 */
export function isReactNative(): boolean {
    // Check for React Native specific globals
    return (typeof navigator !== 'undefined' &&
            typeof navigator.userAgent === 'string' &&
            navigator.userAgent.includes('ReactNative')) ||
           // Alternative check for React Native environment (Hermes engine)
           (typeof global !== 'undefined' &&
            typeof (global as any).HermesInternal !== 'undefined') ||
           // Check for React Native specific modules (only in actual RN environment)
           (typeof require !== 'undefined' &&
            typeof global !== 'undefined' &&
            (() => {
                try {
                    // Only try to require react-native if we're likely in a RN environment
                    if (typeof (global as any).HermesInternal !== 'undefined' ||
                        typeof (global as any).__METRO__ !== 'undefined') {
                        return eval('require')('react-native') !== undefined;
                    }
                    return false;
                } catch {
                    return false;
                }
            })());
}

/**
 * Check if running in a server-side rendering context
 */
export function isSSR(): boolean {
    return typeof window === 'undefined' && 
           typeof global !== 'undefined';
}

/**
 * Check if localStorage is available
 */
export function isLocalStorageAvailable(): boolean {
    try {
        if (typeof window === 'undefined' || !window.localStorage) {
            return false;
        }

        const testKey = '__localStorage_test__';
        window.localStorage.setItem(testKey, 'test');
        window.localStorage.removeItem(testKey);
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * Check if IndexedDB is available
 */
export function isIndexedDBAvailable(): boolean {
    try {
        return typeof window !== 'undefined' && 
               'indexedDB' in window && 
               window.indexedDB !== null;
    } catch (error) {
        return false;
    }
}

/**
 * Check if WebSQL is available (deprecated but still used as fallback)
 */
export function isWebSQLAvailable(): boolean {
    try {
        return typeof window !== 'undefined' && 
               'openDatabase' in window;
    } catch (error) {
        return false;
    }
}

/**
 * Check if AsyncStorage is available (React Native)
 */
export function isAsyncStorageAvailable(): boolean {
    try {
        // Try to import AsyncStorage dynamically
        return isReactNative() && 
               typeof require !== 'undefined';
    } catch (error) {
        return false;
    }
}

/**
 * Get user agent string (browser only)
 */
function getUserAgent(): string | undefined {
    try {
        return typeof navigator !== 'undefined' ? navigator.userAgent : undefined;
    } catch (error) {
        return undefined;
    }
}

/**
 * Get Node.js version (Node.js only)
 */
function getNodeVersion(): string | undefined {
    try {
        return typeof process !== 'undefined' ? process.version : undefined;
    } catch (error) {
        return undefined;
    }
}

/**
 * Get React Native version (React Native only)
 *
 * Attempts to retrieve the actual React Native version using multiple strategies:
 * 1. React Native Platform module constants
 * 2. Package.json version information
 * 3. Global version constants
 * 4. User agent parsing
 * 5. Fallback to environment detection
 */
function getReactNativeVersion(): string | undefined {
    try {
        // Early return if not in React Native environment
        if (!isReactNative()) {
            return undefined;
        }

        // Strategy 1: Try to get version from React Native Platform module
        const platformVersion = getVersionFromPlatformModule();
        if (platformVersion) {
            return platformVersion;
        }

        // Strategy 2: Try to get version from package.json (if accessible)
        const packageVersion = getVersionFromPackageJson();
        if (packageVersion) {
            return packageVersion;
        }

        // Strategy 3: Try to get version from global constants
        const globalVersion = getVersionFromGlobalConstants();
        if (globalVersion) {
            return globalVersion;
        }

        // Strategy 4: Try to parse version from user agent
        const userAgentVersion = getVersionFromUserAgent();
        if (userAgentVersion) {
            return userAgentVersion;
        }

        // Strategy 5: Check for Hermes engine and provide environment info
        const environmentInfo = getReactNativeEnvironmentInfo();
        if (environmentInfo) {
            return environmentInfo;
        }

        // Fallback: Return generic version info
        return 'detected-unknown-version';
    } catch (error) {
        return undefined;
    }
}

/**
 * Attempt to get React Native version from Platform module
 */
function getVersionFromPlatformModule(): string | undefined {
    try {
        // Only try to require react-native if we're in a likely RN environment
        if (typeof require !== 'undefined' &&
            typeof global !== 'undefined' &&
            (typeof (global as any).HermesInternal !== 'undefined' ||
             typeof (global as any).__METRO__ !== 'undefined')) {
            // Try to access React Native Platform module
            const Platform = eval('require')('react-native').Platform;
            if (Platform && Platform.constants) {
                // Different React Native versions expose version info differently
                if (Platform.constants.reactNativeVersion) {
                    const version = Platform.constants.reactNativeVersion;
                    return `${version.major}.${version.minor}.${version.patch}${version.prerelease ? `-${version.prerelease}` : ''}`;
                }

                // Some versions might have it under different paths
                if (Platform.constants.Release) {
                    return `platform-${Platform.constants.Release}`;
                }
            }
        }
    } catch (error) {
        // Platform module not available or accessible
    }
    return undefined;
}

/**
 * Attempt to get React Native version from package.json
 */
function getVersionFromPackageJson(): string | undefined {
    try {
        // Only try to require react-native package.json if we're in a likely RN environment
        if (typeof require !== 'undefined' &&
            typeof global !== 'undefined' &&
            (typeof (global as any).HermesInternal !== 'undefined' ||
             typeof (global as any).__METRO__ !== 'undefined')) {
            // Try to access package.json
            const packageJson = eval('require')('react-native/package.json');
            if (packageJson && packageJson.version) {
                return `package-${packageJson.version}`;
            }
        }
    } catch (error) {
        // Package.json not accessible
    }
    return undefined;
}

/**
 * Attempt to get React Native version from global constants
 */
function getVersionFromGlobalConstants(): string | undefined {
    try {
        // Check for global React Native version constants
        if (typeof global !== 'undefined') {
            const globalObj = global as any;

            // Check for Hermes engine version (common in newer RN versions)
            if (globalObj.HermesInternal && globalObj.HermesInternal.getRuntimeProperties) {
                const hermesProps = globalObj.HermesInternal.getRuntimeProperties();
                if (hermesProps && hermesProps['OSS Release Version']) {
                    return `hermes-${hermesProps['OSS Release Version']}`;
                }
            }

            // Check for other global version indicators
            if (globalObj.__REACT_NATIVE_VERSION__) {
                return `global-${globalObj.__REACT_NATIVE_VERSION__}`;
            }

            if (globalObj.ReactNativeVersion) {
                return `global-${globalObj.ReactNativeVersion}`;
            }
        }
    } catch (error) {
        // Global constants not available
    }
    return undefined;
}

/**
 * Attempt to parse React Native version from user agent
 */
function getVersionFromUserAgent(): string | undefined {
    try {
        if (typeof navigator !== 'undefined' && navigator.userAgent) {
            const userAgent = navigator.userAgent;

            // Look for React Native version patterns in user agent
            const rnVersionMatch = userAgent.match(/ReactNative\/([0-9]+\.[0-9]+\.[0-9]+(?:-[a-zA-Z0-9]+)?)/);
            if (rnVersionMatch && rnVersionMatch[1]) {
                return `useragent-${rnVersionMatch[1]}`;
            }

            // Look for other version indicators
            const versionMatch = userAgent.match(/Version\/([0-9]+\.[0-9]+(?:\.[0-9]+)?)/);
            if (versionMatch && versionMatch[1] && userAgent.includes('ReactNative')) {
                return `useragent-inferred-${versionMatch[1]}`;
            }
        }
    } catch (error) {
        // User agent parsing failed
    }
    return undefined;
}

/**
 * Get React Native environment information as fallback
 */
function getReactNativeEnvironmentInfo(): string | undefined {
    try {
        const envInfo: string[] = [];

        // Check for development vs production
        if (typeof (global as any).__DEV__ !== 'undefined') {
            envInfo.push((global as any).__DEV__ ? 'dev' : 'prod');
        } else if (typeof process !== 'undefined' && process.env.NODE_ENV) {
            envInfo.push(process.env.NODE_ENV === 'development' ? 'dev' : 'prod');
        }

        // Check for Hermes engine
        if (typeof global !== 'undefined' && typeof (global as any).HermesInternal !== 'undefined') {
            envInfo.push('hermes');
        }

        // Check for Metro bundler
        if (typeof global !== 'undefined' && typeof (global as any).__METRO__ !== 'undefined') {
            envInfo.push('metro');
        }

        // Check for specific React Native APIs
        if (typeof require !== 'undefined' &&
            typeof global !== 'undefined' &&
            (typeof (global as any).HermesInternal !== 'undefined' ||
             typeof (global as any).__METRO__ !== 'undefined')) {
            try {
                // Use dynamic import to avoid webpack warnings
                const AsyncStorage = eval('require')('@react-native-async-storage/async-storage');
                if (AsyncStorage) {
                    envInfo.push('async-storage');
                }
            } catch {
                // AsyncStorage not available
            }
        }

        if (envInfo.length > 0) {
            return `env-${envInfo.join('-')}`;
        }
    } catch (error) {
        // Environment detection failed
    }
    return undefined;
}

/**
 * Get detailed React Native information including version, engine, and capabilities
 *
 * @returns Object containing detailed React Native environment information
 */
export function getReactNativeInfo(): {
    version: string | undefined;
    engine: string | undefined;
    bundler: string | undefined;
    isDevelopment: boolean | undefined;
    capabilities: string[];
} | undefined {
    try {
        if (!isReactNative()) {
            return undefined;
        }

        const info = {
            version: getReactNativeVersion(),
            engine: getReactNativeEngine(),
            bundler: getReactNativeBundler(),
            isDevelopment: getReactNativeDevelopmentMode(),
            capabilities: getReactNativeCapabilities()
        };

        return info;
    } catch (error) {
        return undefined;
    }
}

/**
 * Detect the JavaScript engine used in React Native
 */
function getReactNativeEngine(): string | undefined {
    try {
        if (typeof global !== 'undefined') {
            const globalObj = global as any;

            // Check for Hermes engine
            if (globalObj.HermesInternal) {
                return 'hermes';
            }

            // Check for JSC (JavaScriptCore)
            if (globalObj.nativePerformanceNow || globalObj.nativeFlushQueueImmediate) {
                return 'jsc';
            }

            // Check for V8 (less common in RN but possible)
            if (globalObj.v8 || globalObj.chrome) {
                return 'v8';
            }
        }

        return 'unknown';
    } catch (error) {
        return undefined;
    }
}

/**
 * Detect the bundler used in React Native
 */
function getReactNativeBundler(): string | undefined {
    try {
        if (typeof global !== 'undefined') {
            const globalObj = global as any;

            // Check for Metro bundler
            if (globalObj.__METRO__ || globalObj.__metro) {
                return 'metro';
            }

            // Check for Webpack (if using react-native-web or custom setup)
            if (globalObj.__webpack_require__) {
                return 'webpack';
            }
        }

        return 'unknown';
    } catch (error) {
        return undefined;
    }
}

/**
 * Detect if React Native is running in development mode
 */
function getReactNativeDevelopmentMode(): boolean | undefined {
    try {
        // Check global __DEV__ flag
        if (typeof global !== 'undefined' && typeof (global as any).__DEV__ !== 'undefined') {
            return (global as any).__DEV__;
        }

        // Check process.env.NODE_ENV
        if (typeof process !== 'undefined' && process.env.NODE_ENV) {
            return process.env.NODE_ENV === 'development';
        }

        return undefined;
    } catch (error) {
        return undefined;
    }
}

/**
 * Get available React Native capabilities and APIs
 */
function getReactNativeCapabilities(): string[] {
    const capabilities: string[] = [];

    try {
        // Only try to require React Native modules if we're in a likely RN environment
        if (typeof require !== 'undefined' &&
            typeof global !== 'undefined' &&
            (typeof (global as any).HermesInternal !== 'undefined' ||
             typeof (global as any).__METRO__ !== 'undefined')) {
            // Check for common React Native modules
            const modules = [
                '@react-native-async-storage/async-storage',
                'react-native-device-info',
                'react-native-permissions',
                'react-native-camera',
                'react-native-geolocation-service',
                'react-native-push-notification'
            ];

            modules.forEach(moduleName => {
                try {
                    // Use eval to avoid webpack warnings
                    eval('require')(moduleName);
                    capabilities.push(moduleName.replace('@react-native-', '').replace('react-native-', ''));
                } catch {
                    // Module not available
                }
            });
        }

        // Check for native APIs
        if (typeof navigator !== 'undefined') {
            if (navigator.geolocation) capabilities.push('geolocation');
            if ((navigator as any).camera) capabilities.push('camera');
            if ((navigator as any).notification) capabilities.push('notifications');
        }

    } catch (error) {
        // Capability detection failed
    }

    return capabilities;
}

/**
 * Get available storage technologies for the current platform
 */
export function getAvailableStorageTechnologies(): string[] {
    const available: string[] = [];

    if (isLocalStorageAvailable()) {
        available.push('localStorage');
    }

    if (isIndexedDBAvailable()) {
        available.push('indexedDB');
    }

    if (isWebSQLAvailable()) {
        available.push('webSQL');
    }

    if (isAsyncStorageAvailable()) {
        available.push('asyncStorage');
    }

    if (isNode()) {
        available.push('fileSystem');
    }

    // Memory storage is always available
    available.push('memory');

    return available;
}

/**
 * Get the recommended storage adapter for the current platform
 */
export function getRecommendedStorageAdapter(): string {
    const platform = detectPlatform();

    switch (platform.platform) {
        case 'browser':
            if (isIndexedDBAvailable()) return 'browser'; // Uses localforage with IndexedDB
            if (isLocalStorageAvailable()) return 'browser'; // Uses localforage with localStorage
            return 'memory';

        case 'node':
            return 'node'; // Uses file system

        case 'react-native':
            return 'react-native'; // Uses AsyncStorage

        default:
            return 'memory'; // Fallback to memory storage
    }
}

/**
 * Check if the current environment supports persistent storage
 */
export function supportsPersistentStorage(): boolean {
    const platform = detectPlatform();
    
    switch (platform.platform) {
        case 'browser':
            return isLocalStorageAvailable() || isIndexedDBAvailable();
        case 'node':
            return true; // File system is persistent
        case 'react-native':
            return isAsyncStorageAvailable();
        default:
            return false;
    }
}

/**
 * Get platform-specific storage limitations
 */
export function getStorageLimitations(): {
    maxSize: number; // in bytes, -1 for unlimited
    supportsComplexTypes: boolean;
    supportsTransactions: boolean;
    supportsTTL: boolean;
} {
    const platform = detectPlatform();

    switch (platform.platform) {
        case 'browser':
            return {
                maxSize: isIndexedDBAvailable() ? 50 * 1024 * 1024 : 5 * 1024 * 1024, // 50MB for IndexedDB, 5MB for localStorage
                supportsComplexTypes: true,
                supportsTransactions: isIndexedDBAvailable(),
                supportsTTL: false // We implement TTL ourselves
            };

        case 'node':
            return {
                maxSize: -1, // Limited by disk space
                supportsComplexTypes: true,
                supportsTransactions: false, // We don't implement transactions for file system
                supportsTTL: false // We implement TTL ourselves
            };

        case 'react-native':
            return {
                maxSize: 6 * 1024 * 1024, // 6MB typical limit
                supportsComplexTypes: true,
                supportsTransactions: false,
                supportsTTL: false // We implement TTL ourselves
            };

        default:
            return {
                maxSize: -1,
                supportsComplexTypes: true,
                supportsTransactions: false,
                supportsTTL: false
            };
    }
}
