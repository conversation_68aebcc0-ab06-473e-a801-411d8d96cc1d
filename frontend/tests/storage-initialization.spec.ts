import { test, expect } from '@playwright/test';

test.describe('Storage Initialization', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3000');
  });

  test('should initialize storage system without errors', async ({ page }) => {
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');

    // Check that the page loads successfully (not stuck on loading screen)
    await expect(page.locator('text=Initializing application...')).not.toBeVisible({ timeout: 10000 });

    // Verify the main content is visible
    await expect(page.locator('main')).toBeVisible();
    await expect(page.locator('text=Categories')).toBeVisible();

    // Capture console messages during page load
    const consoleMessages: string[] = [];
    // Capture console messages during page load
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      consoleMessages.push(msg.text());
    });

    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');

    // Reload to capture fresh console messages
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Verify storage initialization messages
    const hasStorageInitMessage = consoleMessages.some(msg => 
      msg.includes('Storage system initialized successfully')
    );
    const hasServicesInitMessage = consoleMessages.some(msg => 
      msg.includes('Services initialized successfully')
    );

    expect(hasStorageInitMessage).toBe(true);
    expect(hasServicesInitMessage).toBe(true);

    // Verify no storage initialization errors
    const hasStorageErrors = consoleMessages.some(msg => 
      msg.includes('Storage manager not initialized') ||
      msg.includes('Failed to initialize storage')
    );
    expect(hasStorageErrors).toBe(false);
  });

  test('should handle repository access after storage initialization', async ({ page }) => {
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');

    // Verify repositories can be accessed without errors
    const repositoryErrors = await page.evaluate(() => {
      const errors: string[] = [];
      
      // Check console for repository-related errors
      const originalError = console.error;
      console.error = (...args) => {
        const message = args.join(' ');
        if (message.includes('RepositoryBase') || message.includes('Storage manager not initialized')) {
          errors.push(message);
        }
        originalError.apply(console, args);
      };

      return errors;
    });

    expect(repositoryErrors.length).toBe(0);
  });

  test('should initialize storage adapters correctly', async ({ page }) => {
    // Capture console messages
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      consoleMessages.push(msg.text());
    });

    await page.waitForLoadState('networkidle');

    // Verify storage adapters are created successfully
    const hasAdapterMessages = consoleMessages.some(msg => 
      msg.includes('Storage adapter initialized successfully')
    );
    expect(hasAdapterMessages).toBe(true);

    // Verify no adapter initialization failures
    const hasAdapterErrors = consoleMessages.some(msg => 
      msg.includes('Failed to initialize storage adapter')
    );
    expect(hasAdapterErrors).toBe(false);
  });

  test('should not show critical dependency warnings in build', async ({ page }) => {
    // This test verifies that the webpack critical dependency warnings are resolved
    // by checking that the page loads without compilation errors
    
    await page.waitForLoadState('networkidle');
    
    // If the page loads successfully, it means the critical dependency warnings
    // didn't prevent compilation
    await expect(page.locator('main')).toBeVisible();
    
    // Verify no compilation errors in console
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleMessages.push(msg.text());
      }
    });

    await page.reload();
    await page.waitForLoadState('networkidle');

    // Filter out expected network errors (API not running)
    const compilationErrors = consoleMessages.filter(msg => 
      !msg.includes('ERR_CONNECTION_REFUSED') &&
      !msg.includes('ERR_NAME_NOT_RESOLVED') &&
      !msg.includes('Network Error')
    );

    expect(compilationErrors.length).toBe(0);
  });

  test('should handle storage operations without blocking UI', async ({ page }) => {
    await page.waitForLoadState('networkidle');

    // Verify the UI is responsive and not blocked by storage operations
    const languageButton = page.locator('button:has-text("English")');
    await expect(languageButton).toBeVisible();
    
    // Test that we can interact with UI elements
    await languageButton.click();
    
    // Verify the language dropdown appears (indicating UI is responsive)
    await expect(page.locator('[role="menu"], [role="listbox"]')).toBeVisible({ timeout: 5000 });
  });

  test('should persist data across page reloads', async ({ page }) => {
    await page.waitForLoadState('networkidle');

    // Wait for session to be created
    await expect(page.locator('text=Guest 1')).toBeVisible();

    // Reload the page
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Verify session data persists
    await expect(page.locator('text=Guest 1')).toBeVisible();

    // Verify no storage errors after reload
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error' && 
          (msg.text().includes('Storage') || msg.text().includes('Repository'))) {
        consoleMessages.push(msg.text());
      }
    });

    // Wait a bit for any async operations
    await page.waitForTimeout(2000);

    const storageErrors = consoleMessages.filter(msg => 
      !msg.includes('ERR_CONNECTION_REFUSED') &&
      !msg.includes('ERR_NAME_NOT_RESOLVED') &&
      !msg.includes('Network Error')
    );

    expect(storageErrors.length).toBe(0);
  });
});
