/**
 * Test utilities and helpers for storage module testing
 */

import { IStorageAdapter, StorageConfig } from '../../types';
import { MemoryStorageAdapter } from '../../adapters/MemoryStorageAdapter';

// Mock localforage
jest.mock('localforage', () => {
  const mockLocalForage: any = {
    INDEXEDDB: 'asyncStorage',
    LOCALSTORAGE: 'localStorageWrapper',
    WEBSQL: 'webSQLStorage',

    createInstance: jest.fn((): any => mockLocalForage),
    config: jest.fn((): any => ({})),
    driver: jest.fn((): string => 'localStorageWrapper'),
    ready: jest.fn((): Promise<void> => Promise.resolve()),
    supports: jest.fn((driver: string): boolean => {
      return ['asyncStorage', 'localStorageWrapper', 'webSQLStorage'].includes(driver);
    }),
    setDriver: jest.fn((): Promise<void> => Promise.resolve()),

    // Storage methods
    getItem: jest.fn((): Promise<any> => Promise.resolve(null)),
    setItem: jest.fn((): Promise<void> => Promise.resolve()),
    removeItem: jest.fn((): Promise<void> => Promise.resolve()),
    clear: jest.fn((): Promise<void> => Promise.resolve()),
    length: jest.fn((): Promise<number> => Promise.resolve(0)),
    keys: jest.fn((): Promise<string[]> => Promise.resolve([])),
    key: jest.fn((): Promise<string | null> => Promise.resolve(null))
  };

  return mockLocalForage;
});

/**
 * Create a test storage adapter with default configuration
 */
export async function createTestStorage(
  storeName: string = 'test_store',
  config?: Partial<StorageConfig>
): Promise<IStorageAdapter> {
  const adapter = new MemoryStorageAdapter();
  await adapter.initialize({
    name: 'test_db',
    storeName,
    ...config
  });
  return adapter;
}

/**
 * Mock browser environment globals
 */
export function mockBrowserEnvironment() {
  const originalWindow = global.window;
  const originalDocument = global.document;
  const originalNavigator = global.navigator;
  const originalLocalStorage = global.localStorage;
  const originalIndexedDB = global.indexedDB;

  // Create a mock storage that behaves like localStorage
  const mockStorage: Storage = {
    data: {} as Record<string, string>,
    getItem: jest.fn((key: string): string | null => {
      return (mockStorage as any).data[key] || null;
    }),
    setItem: jest.fn((key: string, value: string): void => {
      (mockStorage as any).data[key] = value;
    }),
    removeItem: jest.fn((key: string): void => {
      delete (mockStorage as any).data[key];
    }),
    clear: jest.fn((): void => {
      (mockStorage as any).data = {};
    }),
    get length(): number {
      return Object.keys((mockStorage as any).data).length;
    },
    key: jest.fn((index: number): string | null => {
      return Object.keys((mockStorage as any).data)[index] || null;
    })
  } as any;

  // Mock window object
  (global as any).window = {
    localStorage: mockStorage,
    indexedDB: {
      open: jest.fn().mockReturnValue({
        onsuccess: null,
        onerror: null,
        result: {
          createObjectStore: jest.fn(),
          transaction: jest.fn()
        }
      }),
      deleteDatabase: jest.fn(),
      cmp: jest.fn()
    },
    navigator: {
      userAgent: 'Mozilla/5.0 (Test Browser)',
      platform: 'Test',
      storage: {
        estimate: jest.fn().mockResolvedValue({
          usage: 1024,
          quota: 1024 * 1024 * 1024
        })
      }
    },
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  };

  // Mock document
  (global as any).document = {
    createElement: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    visibilityState: 'visible',
    hidden: false
  };

  // Mock navigator
  (global as any).navigator = {
    userAgent: 'Mozilla/5.0 (Test Browser)',
    platform: 'Test',
    storage: {
      estimate: jest.fn().mockResolvedValue({
        usage: 1024,
        quota: 1024 * 1024 * 1024
      })
    }
  };

  // Mock localStorage
  (global as any).localStorage = mockStorage;

  // Mock IndexedDB
  (global as any).indexedDB = {
    open: jest.fn().mockReturnValue({
      onsuccess: null,
      onerror: null,
      result: {
        createObjectStore: jest.fn(),
        transaction: jest.fn()
      }
    }),
    deleteDatabase: jest.fn(),
    cmp: jest.fn()
  };

  return () => {
    global.window = originalWindow;
    global.document = originalDocument;
    global.navigator = originalNavigator;
    global.localStorage = originalLocalStorage;
    global.indexedDB = originalIndexedDB;
  };
}

/**
 * Mock Node.js environment
 */
export function mockNodeEnvironment() {
  const originalProcess = global.process;
  const originalRequire = global.require;

  // Mock process object
  (global as any).process = {
    versions: { node: '18.0.0' },
    env: { NODE_ENV: 'test' },
    cwd: jest.fn(() => '/test/directory'),
    platform: 'linux'
  };

  // Mock require function
  (global as any).require = jest.fn();

  return () => {
    global.process = originalProcess;
    global.require = originalRequire;
  };
}

/**
 * Mock React Native environment
 */
export function mockReactNativeEnvironment() {
  const originalNavigator = global.navigator;
  const originalHermesInternal = (global as any).HermesInternal;
  const originalAsyncStorage = (global as any).AsyncStorage;

  // Mock React Native navigator
  (global as any).navigator = {
    product: 'ReactNative',
    userAgent: 'ReactNative/0.72.0'
  };

  // Mock Hermes engine
  (global as any).HermesInternal = {
    getRuntimeProperties: () => ({
      'OSS Release Version': '0.12.0'
    })
  };

  // Mock AsyncStorage
  (global as any).AsyncStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
    getAllKeys: jest.fn(),
    multiGet: jest.fn(),
    multiSet: jest.fn(),
    multiRemove: jest.fn()
  };

  return () => {
    global.navigator = originalNavigator;
    (global as any).HermesInternal = originalHermesInternal;
    (global as any).AsyncStorage = originalAsyncStorage;
  };
}

/**
 * Mock SSR environment (no window object)
 */
export function mockSSREnvironment() {
  const originalWindow = global.window;
  const originalDocument = global.document;

  // Remove window and document to simulate SSR
  delete (global as any).window;
  delete (global as any).document;

  return () => {
    global.window = originalWindow;
    global.document = originalDocument;
  };
}

/**
 * Create mock file system for Node.js testing
 */
export function createMockFileSystem() {
  const mockFs = {
    promises: {
      mkdir: jest.fn().mockResolvedValue(undefined),
      access: jest.fn().mockResolvedValue(undefined),
      readFile: jest.fn().mockResolvedValue('{}'),
      writeFile: jest.fn().mockResolvedValue(undefined),
      unlink: jest.fn().mockResolvedValue(undefined),
      readdir: jest.fn().mockResolvedValue([])
    }
  };

  const mockPath = {
    join: jest.fn((...args) => args.join('/')),
    dirname: jest.fn((path) => path.split('/').slice(0, -1).join('/')),
    basename: jest.fn((path) => path.split('/').pop())
  };

  return { mockFs, mockPath };
}

/**
 * Create a working mock localforage instance for browser tests
 */
export function createMockLocalforage() {
  const storage = new Map<string, any>();

  const mockInstance = {
    ready: jest.fn(() => Promise.resolve()),
    getItem: jest.fn((key: string) => Promise.resolve(storage.get(key) || null)),
    setItem: jest.fn((key: string, value: any) => {
      storage.set(key, value);
      return Promise.resolve();
    }),
    removeItem: jest.fn((key: string) => {
      storage.delete(key);
      return Promise.resolve();
    }),
    clear: jest.fn(() => {
      storage.clear();
      return Promise.resolve();
    }),
    length: jest.fn(() => Promise.resolve(storage.size)),
    keys: jest.fn(() => Promise.resolve(Array.from(storage.keys()))),
    key: jest.fn((index: number) => {
      const keys = Array.from(storage.keys());
      return Promise.resolve(keys[index] || null);
    }),
    driver: jest.fn(() => 'localStorageWrapper'),
    config: jest.fn(() => ({})),
    setDriver: jest.fn(() => Promise.resolve())
  };

  return mockInstance;
}

/**
 * Wait for a specified amount of time
 */
export function wait(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Generate test data of various types
 */
export const testData = {
  simple: {
    string: 'test string',
    number: 42,
    boolean: true,
    null: null
  },
  complex: {
    array: [1, 2, 3, 'four', { five: 5 }],
    object: {
      nested: {
        deeply: {
          value: 'deep value'
        }
      }
    },
    date: new Date().toISOString(),
    mixed: {
      string: 'test',
      number: 123,
      boolean: false,
      array: ['a', 'b', 'c'],
      object: { key: 'value' }
    }
  },
  large: Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    data: `Item ${i}`,
    timestamp: Date.now() + i,
    metadata: {
      type: 'test',
      category: i % 10,
      tags: [`tag${i % 5}`, `category${i % 3}`]
    }
  }))
};

/**
 * Assert that an adapter has expected capabilities
 */
export function assertAdapterCapabilities(
  adapter: IStorageAdapter,
  expectedPlatform: string,
  expectedPersistent: boolean
) {
  const capabilities = adapter.getCapabilities();
  expect(capabilities.platform).toBe(expectedPlatform);
  expect(capabilities.persistent).toBe(expectedPersistent);
  expect(capabilities.supportsTTL).toBe(true);
  expect(typeof capabilities.maxStorageSize).toBe('number');
}

/**
 * Test basic storage operations
 */
export async function testBasicOperations(adapter: IStorageAdapter) {
  // Test setItem and getItem
  await adapter.setItem('test_key', 'test_value');
  const value = await adapter.getItem('test_key');
  expect(value).toBe('test_value');

  // Test complex data
  const complexData = { nested: { value: 42 }, array: [1, 2, 3] };
  await adapter.setItem('complex_key', complexData);
  const retrievedComplex = await adapter.getItem('complex_key');
  expect(retrievedComplex).toEqual(complexData);

  // Test removeItem
  await adapter.removeItem('test_key');
  const removedValue = await adapter.getItem('test_key');
  expect(removedValue).toBeNull();

  // Test keys and length
  await adapter.setItem('key1', 'value1');
  await adapter.setItem('key2', 'value2');
  
  const keys = await adapter.keys();
  expect(keys).toContain('key1');
  expect(keys).toContain('key2');
  expect(keys).toContain('complex_key');

  const length = await adapter.length();
  expect(length).toBe(3);

  // Test clear
  await adapter.clear();
  const finalLength = await adapter.length();
  expect(finalLength).toBe(0);
}

/**
 * Test TTL functionality
 */
export async function testTTLFunctionality(adapter: IStorageAdapter) {
  const shortTTL = 100; // 100ms
  
  // Set item with TTL
  await adapter.setItem('ttl_key', 'ttl_value', shortTTL);
  
  // Should be available immediately
  let value = await adapter.getItem('ttl_key');
  expect(value).toBe('ttl_value');
  
  // Wait for expiration
  await wait(150);
  
  // Should be expired now
  value = await adapter.getItem('ttl_key');
  expect(value).toBeNull();
}
